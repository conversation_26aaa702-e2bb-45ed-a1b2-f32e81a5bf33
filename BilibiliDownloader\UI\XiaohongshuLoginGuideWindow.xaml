<Window x:Class="BilibiliDownloader.UI.XiaohongshuLoginGuideWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="小红书登录指导" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="小红书Cookie获取指导" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>
        
        <!-- 内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 说明文字 -->
                <TextBlock TextWrapping="Wrap" FontSize="14" Margin="0,0,0,20">
                    <Run Text="小红书使用手机验证码和二维码登录，无法直接输入用户名密码。"/>
                    <LineBreak/>
                    <Run Text="请按照以下步骤获取Cookie："/>
                </TextBlock>
                
                <!-- 步骤1 -->
                <Border Background="#F5F5F5" Padding="15" Margin="0,0,0,15" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="步骤1：打开浏览器登录" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                        <TextBlock TextWrapping="Wrap">
                            1. 打开Chrome、Edge或Firefox浏览器<LineBreak/>
                            2. 访问：https://www.xiaohongshu.com<LineBreak/>
                            3. 点击右上角"登录"按钮
                        </TextBlock>
                    </StackPanel>
                </Border>
                
                <!-- 步骤2 -->
                <Border Background="#F0F8FF" Padding="15" Margin="0,0,0,15" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="步骤2：完成登录" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                        <TextBlock TextWrapping="Wrap">
                            选择以下任一方式登录：<LineBreak/>
                            • 手机号 + 验证码<LineBreak/>
                            • 扫描二维码<LineBreak/>
                            • 微信登录
                        </TextBlock>
                    </StackPanel>
                </Border>
                
                <!-- 步骤3 -->
                <Border Background="#F0FFF0" Padding="15" Margin="0,0,0,15" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="步骤3：获取Cookie" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                        <TextBlock TextWrapping="Wrap">
                            1. 登录成功后，按F12键打开开发者工具<LineBreak/>
                            2. 切换到"Network"（网络）标签<LineBreak/>
                            3. 刷新页面（按F5或Ctrl+R）<LineBreak/>
                            4. 在网络请求列表中找到任意一个请求<LineBreak/>
                            5. 点击请求，在右侧找到"Request Headers"<LineBreak/>
                            6. 找到"Cookie:"行，复制整个Cookie值
                        </TextBlock>
                    </StackPanel>
                </Border>
                
                <!-- 步骤4 -->
                <Border Background="#FFF8DC" Padding="15" Margin="0,0,0,15" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="步骤4：添加到下载器" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                        <TextBlock TextWrapping="Wrap">
                            1. 回到下载器，点击"账号管理"按钮<LineBreak/>
                            2. 选择"小红书"平台<LineBreak/>
                            3. 将复制的Cookie粘贴到Cookie输入框<LineBreak/>
                            4. 点击"验证"按钮测试Cookie是否有效<LineBreak/>
                            5. 验证成功后点击"保存"
                        </TextBlock>
                    </StackPanel>
                </Border>
                
                <!-- 注意事项 -->
                <Border Background="#FFE4E1" Padding="15" Margin="0,0,0,15" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="⚠️ 注意事项" FontWeight="Bold" FontSize="16" Margin="0,0,0,10" Foreground="Red"/>
                        <TextBlock TextWrapping="Wrap">
                            • Cookie会定期过期，需要重新获取<LineBreak/>
                            • 不要在公共电脑上保存Cookie<LineBreak/>
                            • Cookie包含敏感信息，请妥善保管<LineBreak/>
                            • 如果下载失败，请尝试重新获取Cookie
                        </TextBlock>
                    </StackPanel>
                </Border>
                
                <!-- 快捷链接 -->
                <Border Background="#E6E6FA" Padding="15" Margin="0,0,0,15" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="🔗 快捷操作" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                        <StackPanel Orientation="Horizontal">
                            <Button Name="OpenXiaohongshuButton" Content="打开小红书官网" 
                                    Padding="10,5" Margin="0,0,10,0" Click="OpenXiaohongshuButton_Click"/>
                            <Button Name="OpenAccountManagerButton" Content="打开账号管理" 
                                    Padding="10,5" Click="OpenAccountManagerButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="CloseButton" Content="我知道了" Padding="20,8" Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
