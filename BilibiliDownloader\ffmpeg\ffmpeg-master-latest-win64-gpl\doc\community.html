<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by , GNU Texinfo 7.1 -->
  <head>
    <meta charset="utf-8">
    <title>
      Community
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      Community
      </h1>


<a name="SEC_Top"></a>

<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Organisation-1" href="#Organisation-1">1 Organisation</a></li>
  <li><a id="toc-General-Assembly-1" href="#General-Assembly-1">2 General Assembly</a></li>
  <li><a id="toc-Voting-1" href="#Voting-1">3 Voting</a></li>
  <li><a id="toc-Technical-Committee-1" href="#Technical-Committee-1">4 Technical Committee</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Resolution-Process-1" href="#Resolution-Process-1">4.1 Resolution Process</a>
    <ul class="toc-numbered-mark">
      <li><a id="toc-Seizing" href="#Seizing">4.1.1 Seizing</a></li>
      <li><a id="toc-Announcement" href="#Announcement">4.1.2 Announcement</a></li>
      <li><a id="toc-RFC-call" href="#RFC-call">4.1.3 RFC call</a></li>
      <li><a id="toc-Within-TC" href="#Within-TC">4.1.4 Within TC</a></li>
      <li><a id="toc-Decisions" href="#Decisions">4.1.5 Decisions</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Community-Committee-1" href="#Community-Committee-1">5 Community Committee</a></li>
  <li><a id="toc-Code-of-Conduct-1" href="#Code-of-Conduct-1">6 Code of Conduct</a></li>
</ul>
</div>
</div>

<a class="anchor" id="Organisation"></a><a name="Organisation-1"></a>
<h2 class="chapter">1 Organisation<span class="pull-right"><a class="anchor hidden-xs" href="#Organisation-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Organisation-1" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg project is organized through a community working on global consensus.
</p>
<p>Decisions are taken by the ensemble of active members, through voting and are aided by two committees.
</p>
<a class="anchor" id="General-Assembly"></a><a name="General-Assembly-1"></a>
<h2 class="chapter">2 General Assembly<span class="pull-right"><a class="anchor hidden-xs" href="#General-Assembly-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-General-Assembly-1" aria-hidden="true">TOC</a></span></h2>

<p>The ensemble of active members is called the General Assembly (GA).
</p>
<p>The General Assembly is sovereign and legitimate for all its decisions regarding the FFmpeg project.
</p>
<p>The General Assembly is made up of active contributors.
</p>
<p>Contributors are considered &quot;active contributors&quot; if they have authored more than 20 patches in the last 36 months in the main FFmpeg repository, or if they have been voted in by the GA.
</p>
<p>The list of active contributors is updated twice each year, on 1st January and 1st July, 0:00 UTC.
</p>
<p>Additional members are added to the General Assembly through a vote after proposal by a member of the General Assembly. They are part of the GA for two years, after which they need a confirmation by the GA.
</p>
<p>A script to generate the current members of the general assembly (minus members voted in) can be found in &lsquo;tools/general_assembly.pl&lsquo;.
</p>
<a class="anchor" id="Voting"></a><a name="Voting-1"></a>
<h2 class="chapter">3 Voting<span class="pull-right"><a class="anchor hidden-xs" href="#Voting-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Voting-1" aria-hidden="true">TOC</a></span></h2>

<p>Voting is done using a ranked voting system, currently running on https://vote.ffmpeg.org/ .
</p>
<p>Majority vote means more than 50% of the expressed ballots.
</p>
<a class="anchor" id="Technical-Committee"></a><a name="Technical-Committee-1"></a>
<h2 class="chapter">4 Technical Committee<span class="pull-right"><a class="anchor hidden-xs" href="#Technical-Committee-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Technical-Committee-1" aria-hidden="true">TOC</a></span></h2>

<p>The Technical Committee (TC) is here to arbitrate and make decisions when technical conflicts occur in the project. They will consider the merits of all the positions, judge them and make a decision.
</p>
<p>The TC resolves technical conflicts but is not a technical steering committee.
</p>
<p>Decisions by the TC are binding for all the contributors.
</p>
<p>Decisions made by the TC can be re-opened after 1 year or by a majority vote of the General Assembly, requested by one of the member of the GA.
</p>
<p>The TC is elected by the General Assembly for a duration of 1 year, and is composed of 5 members. Members can be re-elected if they wish. A majority vote in the General Assembly can trigger a new election of the TC.
</p>
<p>The members of the TC can be elected from outside of the GA. Candidates for election can either be suggested or self-nominated.
</p>
<p>The conflict resolution process is detailed in the resolution process document.
</p>
<p>The TC can be contacted at &lt;tc@ffmpeg&gt;.
</p>
<a class="anchor" id="Resolution-Process"></a><a name="Resolution-Process-1"></a>
<h3 class="section">4.1 Resolution Process<span class="pull-right"><a class="anchor hidden-xs" href="#Resolution-Process-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Resolution-Process-1" aria-hidden="true">TOC</a></span></h3>

<p>The Technical Committee (TC) is here to arbitrate and make decisions when technical conflicts occur in the project.
</p>
<p>The TC main role is to resolve technical conflicts. It is therefore not a technical steering committee, but it is understood that some decisions might impact the future of the project.
</p>
<a name="Seizing"></a>
<h4 class="subsection">4.1.1 Seizing<span class="pull-right"><a class="anchor hidden-xs" href="#Seizing" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Seizing" aria-hidden="true">TOC</a></span></h4>

<p>The TC can take possession of any technical matter that it sees fit.
</p>
<p>To involve the TC in a matter, email tc&nbsp;or CC them on an ongoing discussion.
</p>
<p>As members of TC are developers, they also can email tc&nbsp;to raise an issue.
</p><a name="Announcement"></a>
<h4 class="subsection">4.1.2 Announcement<span class="pull-right"><a class="anchor hidden-xs" href="#Announcement" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Announcement" aria-hidden="true">TOC</a></span></h4>

<p>The TC, once seized, must announce itself on the main mailing list, with a [TC] tag.
</p>
<p>The TC has 2 modes of operation: a RFC one and an internal one.
</p>
<p>If the TC thinks it needs the input from the larger community, the TC can call for a RFC. Else, it can decide by itself.
</p>
<p>The decision to use a RFC process or an internal discussion is a discretionary decision of the TC.
</p>
<p>The TC can also reject a seizure for a few reasons such as: the matter was not discussed enough previously; it lacks expertise to reach a beneficial decision on the matter; or the matter is too trivial.
</p><a name="RFC-call"></a>
<h4 class="subsection">4.1.3 RFC call<span class="pull-right"><a class="anchor hidden-xs" href="#RFC-call" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-RFC-call" aria-hidden="true">TOC</a></span></h4>

<p>In the RFC mode, one person from the TC posts on the mailing list the technical question and will request input from the community.
</p>
<p>The mail will have the following specification:
</p>
<p>a precise title
    a specific tag [TC RFC]
    a top-level email
    contain a precise question that does not exceed 100 words and that is answerable by developers
    may have an extra description, or a link to a previous discussion, if deemed necessary,
    contain a precise end date for the answers.
</p>
<p>The answers from the community must be on the main mailing list and must have the following specification:
</p>
<p>keep the tag and the title unchanged
    limited to 400 words
    a first-level, answering directly to the main email
    answering to the question.
</p>
<p>Further replies to answers are permitted, as long as they conform to the community standards of politeness, they are limited to 100 words, and are not nested more than once. (max-depth=2)
</p>
<p>After the end-date, mails on the thread will be ignored.
</p>
<p>Violations of those rules will be escalated through the Community Committee.
</p>
<p>After all the emails are in, the TC has 96 hours to give its final decision. Exceptionally, the TC can request an extra delay, that will be notified on the mailing list.
</p><a name="Within-TC"></a>
<h4 class="subsection">4.1.4 Within TC<span class="pull-right"><a class="anchor hidden-xs" href="#Within-TC" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Within-TC" aria-hidden="true">TOC</a></span></h4>

<p>In the internal case, the TC has 96 hours to give its final decision. Exceptionally, the TC can request an extra delay.
</p><a name="Decisions"></a>
<h4 class="subsection">4.1.5 Decisions<span class="pull-right"><a class="anchor hidden-xs" href="#Decisions" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Decisions" aria-hidden="true">TOC</a></span></h4>

<p>The decisions from the TC will be sent on the mailing list, with the [TC] tag.
</p>
<p>Internally, the TC should take decisions with a majority, or using ranked-choice voting.
</p>
<p>Each TC member must vote on such decision according to what is, in their view, best for the project.
</p>
<p>If a TC member feels they are affected by a conflict of interest with regards to the case, they should announce it and recuse themselves from the TC
discussion and vote.
</p>
<p>A conflict of interest is presumed to occur when a TC member has a personal interest (e.g. financial) in a specific outcome of the case.
</p>
<p>The decision from the TC should be published with a summary of the reasons that lead to this decision.
</p>
<p>The decisions from the TC are final, until the matters are reopened after no less than one year.
</p>
<a class="anchor" id="Community-Committee"></a><a name="Community-Committee-1"></a>
<h2 class="chapter">5 Community Committee<span class="pull-right"><a class="anchor hidden-xs" href="#Community-Committee-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Community-Committee-1" aria-hidden="true">TOC</a></span></h2>

<p>The Community Committee (CC) is here to arbitrage and make decisions when inter-personal conflicts occur in the project. It will decide quickly and take actions, for the sake of the project.
</p>
<p>The CC can remove privileges of offending members, including removal of commit access and temporary ban from the community.
</p>
<p>Decisions made by the CC can be re-opened after 1 year or by a majority vote of the General Assembly. Indefinite bans from the community must be confirmed by the General Assembly, in a majority vote.
</p>
<p>The CC is elected by the General Assembly for a duration of 1 year, and is composed of 5 members. Members can be re-elected if they wish. A majority vote in the General Assembly can trigger a new election of the CC.
</p>
<p>The members of the CC can be elected from outside of the GA. Candidates for election can either be suggested or self-nominated.
</p>
<p>The CC is governed by and responsible for enforcing the Code of Conduct.
</p>
<p>The CC can be contacted at &lt;cc@ffmpeg&gt;.
</p>
<a class="anchor" id="Code-of-Conduct"></a><a name="Code-of-Conduct-1"></a>
<h2 class="chapter">6 Code of Conduct<span class="pull-right"><a class="anchor hidden-xs" href="#Code-of-Conduct-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Code-of-Conduct-1" aria-hidden="true">TOC</a></span></h2>

<p>Be friendly and respectful towards others and third parties.
Treat others the way you yourself want to be treated.
</p>
<p>Be considerate. Not everyone shares the same viewpoint and priorities as you do.
Different opinions and interpretations help the project.
Looking at issues from a different perspective assists development.
</p>
<p>Do not assume malice for things that can be attributed to incompetence. Even if
it is malice, it&rsquo;s rarely good to start with that as initial assumption.
</p>
<p>Stay friendly even if someone acts contrarily. Everyone has a bad day
once in a while.
If you yourself have a bad day or are angry then try to take a break and reply
once you are calm and without anger if you have to.
</p>
<p>Try to help other team members and cooperate if you can.
</p>
<p>The goal of software development is to create technical excellence, not for any
individual to be better and &quot;win&quot; against the others. Large software projects
are only possible and successful through teamwork.
</p>
<p>If someone struggles do not put them down. Give them a helping hand
instead and point them in the right direction.
</p>
<p>Finally, keep in mind the immortal words of Bill and Ted,
&quot;Be excellent to each other.&quot;
</p>
      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
