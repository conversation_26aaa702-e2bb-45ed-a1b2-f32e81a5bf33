using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core
{
    /// <summary>
    /// BBDown工具包装器，用于调用外部BBDown程序下载番剧
    /// </summary>
    public class BBDownWrapper
    {
        private readonly string _bbdownPath;
        private readonly string _downloadDirectory;

        public BBDownWrapper(string downloadDirectory = null)
        {
            _downloadDirectory = downloadDirectory ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Downloads");
            _bbdownPath = FindBBDownExecutable();
        }

        /// <summary>
        /// 查找BBDown可执行文件
        /// </summary>
        private string FindBBDownExecutable()
        {
            // 1. 检查是否安装了dotnet tool版本
            try
            {
                var result = RunCommand("dotnet", "tool list -g");
                if (result.Contains("bbdown"))
                {
                    Logger.Instance.Info("发现BBDown dotnet tool版本");
                    return "bbdown"; // 使用全局命令
                }
            }
            catch
            {
                // 忽略错误
            }

            // 2. 检查当前目录是否有BBDown.exe
            var localPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "BBDown.exe");
            if (File.Exists(localPath))
            {
                Logger.Instance.Info($"发现本地BBDown: {localPath}");
                return localPath;
            }

            // 3. 检查PATH环境变量
            try
            {
                var result = RunCommand("where", "BBDown.exe");
                if (!string.IsNullOrEmpty(result) && File.Exists(result.Trim()))
                {
                    Logger.Instance.Info($"发现系统PATH中的BBDown: {result.Trim()}");
                    return result.Trim();
                }
            }
            catch
            {
                // 忽略错误
            }

            Logger.Instance.Warning("未找到BBDown可执行文件");
            return null;
        }

        /// <summary>
        /// 检查BBDown是否可用
        /// </summary>
        public bool IsAvailable()
        {
            return !string.IsNullOrEmpty(_bbdownPath);
        }

        /// <summary>
        /// 使用BBDown下载番剧
        /// </summary>
        public async Task<bool> DownloadBangumiAsync(string url, string cookie = null, bool downloadAll = false)
        {
            if (!IsAvailable())
            {
                Logger.Instance.Error("BBDown不可用，无法下载番剧");
                return false;
            }

            try
            {
                Logger.Instance.Info($"使用BBDown下载番剧: {url}");

                // 构建命令参数
                var args = "";
                
                // 添加cookie
                if (!string.IsNullOrEmpty(cookie))
                {
                    args += $"-c \"{cookie}\" ";
                }

                // 添加工作目录
                args += $"--work-dir \"{_downloadDirectory}\" ";

                // 是否下载全集
                if (downloadAll)
                {
                    args += "-p ALL ";
                }

                // 使用TV端API（通常更稳定）
                args += "-tv ";

                // 添加URL
                args += $"\"{url}\"";

                Logger.Instance.Info($"BBDown命令: {_bbdownPath} {args}");

                // 执行BBDown
                var result = await RunCommandAsync(_bbdownPath, args);
                
                if (result.ExitCode == 0)
                {
                    Logger.Instance.Info("BBDown下载完成");
                    return true;
                }
                else
                {
                    Logger.Instance.Error($"BBDown下载失败，退出码: {result.ExitCode}");
                    Logger.Instance.Error($"错误输出: {result.Error}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"BBDown下载异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取番剧信息（仅解析不下载）
        /// </summary>
        public async Task<string> GetBangumiInfoAsync(string url, string cookie = null)
        {
            if (!IsAvailable())
            {
                return null;
            }

            try
            {
                var args = "--only-show-info ";
                
                if (!string.IsNullOrEmpty(cookie))
                {
                    args += $"-c \"{cookie}\" ";
                }

                args += $"\"{url}\"";

                var result = await RunCommandAsync(_bbdownPath, args);
                return result.Output;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取番剧信息异常: {ex.Message}");
                return null;
            }
        }

        private string RunCommand(string command, string args)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = command,
                        Arguments = args,
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                return output;
            }
            catch
            {
                return "";
            }
        }

        private async Task<CommandResult> RunCommandAsync(string command, string args)
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = command,
                    Arguments = args,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };

            process.Start();
            
            var outputTask = process.StandardOutput.ReadToEndAsync();
            var errorTask = process.StandardError.ReadToEndAsync();
            
            await process.WaitForExitAsync();
            
            var output = await outputTask;
            var error = await errorTask;

            return new CommandResult
            {
                ExitCode = process.ExitCode,
                Output = output,
                Error = error
            };
        }

        private class CommandResult
        {
            public int ExitCode { get; set; }
            public string Output { get; set; } = "";
            public string Error { get; set; } = "";
        }
    }
}
