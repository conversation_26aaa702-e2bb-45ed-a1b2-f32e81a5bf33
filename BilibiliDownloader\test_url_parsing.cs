using System;
using System.Text.RegularExpressions;

namespace BilibiliDownloader.Test
{
    public class VideoUrlInfo
    {
        public string VideoId { get; set; }
        public int Page { get; set; } = 1;
    }
    
    public class UrlParsingTest
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("Bilibili URL解析测试");
            Console.WriteLine("=" * 50);
            
            var testUrls = new[]
            {
                "https://www.bilibili.com/video/BV1PyuqznEyG?spm_id_from=333.788.videopod.episodes&vd_source=65708e9424b5f3409807c8675f4da097&p=8",
                "https://www.bilibili.com/video/BV1tMhNe1ELf",
                "https://www.bilibili.com/video/BV1tMhNe1ELf?p=2",
                "https://www.bilibili.com/video/av123456?p=3",
                "https://b23.tv/abc123",
                "BV1PyuqznEyG",
                "av123456"
            };
            
            foreach (var url in testUrls)
            {
                Console.WriteLine($"\n测试URL: {url}");
                var result = ExtractVideoUrlInfo(url);
                Console.WriteLine($"  视频ID: {result.VideoId}");
                Console.WriteLine($"  页面: {result.Page}");
            }
            
            Console.WriteLine("\n测试完成");
        }
        
        private static VideoUrlInfo ExtractVideoUrlInfo(string url)
        {
            try
            {
                var urlInfo = new VideoUrlInfo();
                
                // 支持多种bilibili链接格式
                var patterns = new[]
                {
                    @"bilibili\.com/video/(BV[A-Za-z0-9]+)(?:\?.*?p=(\d+))?",  // BV号带页面参数
                    @"bilibili\.com/video/av(\d+)(?:\?.*?p=(\d+))?",           // AV号带页面参数
                    @"b23\.tv/([A-Za-z0-9]+)",                                 // 短链接
                    @"bilibili\.com/video/([A-Za-z0-9]+)(?:\?.*?p=(\d+))?",   // 通用格式
                    @"^(?:bv|BV)([A-Za-z0-9]+)$",                              // 直接BV号
                    @"^(?:av|AV)(\d+)$"                                        // 直接AV号
                };
                
                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(url, pattern, RegexOptions.IgnoreCase);
                    if (match.Success)
                    {
                        var videoId = match.Groups[1].Value;
                        
                        // 提取页面参数
                        if (match.Groups.Count > 2 && !string.IsNullOrEmpty(match.Groups[2].Value))
                        {
                            if (int.TryParse(match.Groups[2].Value, out int page))
                            {
                                urlInfo.Page = page;
                            }
                        }
                        
                        // 如果是AV号，需要转换为BV号或直接使用
                        if (Regex.IsMatch(videoId, @"^\d+$"))
                        {
                            // 这是AV号
                            urlInfo.VideoId = "av" + videoId;
                        }
                        else
                        {
                            // 确保BV号格式正确
                            if (!videoId.StartsWith("BV", StringComparison.OrdinalIgnoreCase))
                            {
                                videoId = "BV" + videoId;
                            }
                            urlInfo.VideoId = videoId;
                        }
                        
                        return urlInfo;
                    }
                }
                
                // 如果没有匹配到，尝试从URL参数中提取页面信息
                if (url.Contains("?p="))
                {
                    var pageMatch = Regex.Match(url, @"[?&]p=(\d+)");
                    if (pageMatch.Success && int.TryParse(pageMatch.Groups[1].Value, out int pageFromQuery))
                    {
                        urlInfo.Page = pageFromQuery;
                    }
                }
                
                return urlInfo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析URL信息失败: {ex.Message}");
                return new VideoUrlInfo();
            }
        }
    }
}
