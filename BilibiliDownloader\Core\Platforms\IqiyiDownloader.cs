using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class IqiyiDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "爱奇艺";
        public string PlatformIcon => "🎭";
        public bool RequiresLogin => true; // 爱奇艺大部分内容需要登录

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?iqiyi\.com/v_[\w]+\.html",
            @"https?://(?:www\.)?iqiyi\.com/a_[\w]+\.html",
            @"https?://(?:www\.)?iqiyi\.com/dianying/[\w]+\.html",
            @"https?://(?:www\.)?iqiyi\.com/dongman/[\w]+\.html",
            @"https?://m\.iqiyi\.com/v_[\w]+\.html"
        };

        public IqiyiDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析爱奇艺视频: {url}");

                if (string.IsNullOrEmpty(cookie))
                {
                    Logger.Instance.Warning("爱奇艺建议登录以获取更好的解析效果");
                }

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                var videoId = ExtractVideoId(url);
                if (string.IsNullOrEmpty(videoId))
                {
                    throw new Exception("无法提取视频ID");
                }

                // 获取视频信息
                var videoInfo = await GetIqiyiVideoInfoAsync(videoId, url);
                if (videoInfo != null)
                {
                    videoInfo.Platform = PlatformName;
                    videoInfo.OriginalUrl = url;
                }

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"爱奇艺解析失败: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> GetIqiyiVideoInfoAsync(string videoId, string url)
        {
            try
            {
                // 获取网页内容
                var pageContent = await _httpClient.GetStringAsync(url);

                // 从页面中提取视频信息
                var title = ExtractTitleFromPage(pageContent) ?? $"爱奇艺视频 {videoId}";
                var author = ExtractAuthorFromPage(pageContent) ?? "爱奇艺";
                var description = ExtractDescriptionFromPage(pageContent) ?? "";
                var coverUrl = ExtractCoverFromPage(pageContent) ?? "";
                var duration = ExtractDurationFromPage(pageContent);
                var isVip = CheckIfVipContent(pageContent);

                return new VideoInfo
                {
                    VideoId = videoId,
                    Title = title,
                    Author = author,
                    Description = description,
                    CoverUrl = coverUrl,
                    Duration = duration,
                    ViewCount = 0, // 爱奇艺不公开具体播放数
                    PublishTime = DateTime.Now, // 爱奇艺API限制
                    IsCollection = false,
                    IsPaymentRequired = isVip,
                    Pages = new List<VideoPage>
                    {
                        new VideoPage
                        {
                            Page = 1,
                            Cid = 0,
                            Part = title,
                            Duration = duration
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取爱奇艺视频信息失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "4K", Description = "4K超清", Width = 3840, Height = 2160, Format = "mp4" },
                new() { Quality = "1080p", Description = "蓝光1080P", Width = 1920, Height = 1080, Format = "mp4" },
                new() { Quality = "720p", Description = "超清", Width = 1280, Height = 720, Format = "mp4" },
                new() { Quality = "540p", Description = "高清", Width = 960, Height = 540, Format = "mp4" },
                new() { Quality = "360p", Description = "标清", Width = 640, Height = 360, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"获取爱奇艺下载链接: {videoInfo.VideoId}, 质量: {quality}");

                if (string.IsNullOrEmpty(cookie))
                {
                    throw new Exception("爱奇艺下载需要登录，请在账号管理中添加爱奇艺账号");
                }

                // 设置Cookie
                _httpClient.DefaultRequestHeaders.Remove("Cookie");
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);

                // 尝试获取实际的下载链接
                var downloadUrls = await GetIqiyiDownloadUrlsAsync(videoInfo.VideoId, quality);
                
                if (downloadUrls.Count == 0)
                {
                    Logger.Instance.Warning("无法获取爱奇艺直接下载链接");
                    throw new Exception("爱奇艺下载需要特殊处理。由于爱奇艺的版权保护机制，无法直接获取下载链接。");
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取爱奇艺下载链接失败: {ex.Message}");
                throw new Exception($"爱奇艺下载失败: {ex.Message}\n\n建议：\n1. 确保已登录VIP账号\n2. 使用专业的下载工具\n3. 检查视频是否有版权限制");
            }
        }

        private async Task<List<DownloadUrl>> GetIqiyiDownloadUrlsAsync(string videoId, string quality)
        {
            try
            {
                // 注意：由于爱奇艺的版权保护机制，这里只是示例实现
                Logger.Instance.Warning("爱奇艺下载功能需要集成专业工具才能正常工作");
                return new List<DownloadUrl>();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"爱奇艺下载URL获取异常: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                // 验证Cookie是否有效
                _httpClient.DefaultRequestHeaders.Remove("Cookie");
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                
                var response = await _httpClient.GetAsync("https://www.iqiyi.com/");
                var content = await response.Content.ReadAsStringAsync();
                
                // 检查是否已登录
                return content.Contains("isLogin") && content.Contains("true");
            }
            catch
            {
                return false;
            }
        }

        private string? ExtractTitleFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取标题
                var titleMatch = Regex.Match(pageContent, @"<title>([^<]+)</title>");
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Replace("-爱奇艺", "").Trim();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractAuthorFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取制作方信息
                var authorMatch = Regex.Match(pageContent, @"""director"":""([^""]+)""");
                if (authorMatch.Success)
                {
                    return authorMatch.Groups[1].Value;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractDescriptionFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取描述
                var descMatch = Regex.Match(pageContent, @"""description"":""([^""]+)""");
                if (descMatch.Success)
                {
                    return descMatch.Groups[1].Value.Replace("\\n", "\n").Replace("\\\"", "\"");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractCoverFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取封面图
                var coverMatch = Regex.Match(pageContent, @"""img"":""([^""]+)""");
                if (coverMatch.Success)
                {
                    return coverMatch.Groups[1].Value.Replace("\\u002F", "/");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private long ExtractDurationFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取时长
                var durationMatch = Regex.Match(pageContent, @"""duration"":(\d+)");
                if (durationMatch.Success && long.TryParse(durationMatch.Groups[1].Value, out var duration))
                {
                    return duration;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private bool CheckIfVipContent(string pageContent)
        {
            try
            {
                // 检查是否为VIP内容
                return pageContent.Contains("VIP") || pageContent.Contains("会员") || pageContent.Contains("付费");
            }
            catch
            {
                return false;
            }
        }

        private string ExtractVideoId(string url)
        {
            var patterns = new[]
            {
                @"iqiyi\.com/v_([\w]+)\.html",
                @"iqiyi\.com/a_([\w]+)\.html",
                @"dianying/([\w]+)\.html",
                @"dongman/([\w]+)\.html"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
