using System;
using System.ComponentModel;

namespace BilibiliDownloader.Models
{
    public class DownloadItem : INotifyPropertyChanged
    {
        private string _title;
        private string _status;
        private double _progress;
        private string _size;
        private string _speed;
        
        public string VideoId { get; set; }
        public string Url { get; set; }
        public string SavePath { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? CompleteTime { get; set; }
        
        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged(nameof(Title));
            }
        }
        
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }
        
        public double Progress
        {
            get => _progress;
            set
            {
                _progress = value;
                OnPropertyChanged(nameof(Progress));
            }
        }
        
        public string Size
        {
            get => _size;
            set
            {
                _size = value;
                OnPropertyChanged(nameof(Size));
            }
        }
        
        public string Speed
        {
            get => _speed;
            set
            {
                _speed = value;
                OnPropertyChanged(nameof(Speed));
            }
        }
        
        public DownloadItem()
        {
            StartTime = DateTime.Now;
            Status = "等待中";
            Progress = 0;
            Size = "未知";
            Speed = "0 KB/s";
        }
        
        public event PropertyChangedEventHandler? PropertyChanged;
        
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
