using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;
using BilibiliDownloader.Models;

namespace BilibiliDownloader.Core
{
    public class DownloadManager
    {
        private readonly HttpClient _httpClient;
        private readonly List<DownloadTask> _activeTasks;
        private readonly string _downloadDirectory;
        private readonly Dispatcher _dispatcher;

        public event EventHandler<DownloadProgressEventArgs>? ProgressChanged;
        public event EventHandler<DownloadCompletedEventArgs>? DownloadCompleted;
        
        public DownloadManager(string downloadDirectory = null, Dispatcher dispatcher = null)
        {
            _httpClient = new HttpClient();
            _activeTasks = new List<DownloadTask>();
            _dispatcher = dispatcher ?? Dispatcher.CurrentDispatcher;

            // 设置下载目录为程序根目录的Downloads文件夹
            if (downloadDirectory == null)
            {
                var appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var rootDirectory = Directory.GetParent(appDirectory)?.Parent?.Parent?.Parent?.FullName ?? appDirectory;
                _downloadDirectory = Path.Combine(rootDirectory, "Downloads");
            }
            else
            {
                _downloadDirectory = downloadDirectory;
            }

            // 确保下载目录存在
            Directory.CreateDirectory(_downloadDirectory);

            SetupHttpClient();
        }
        
        private void SetupHttpClient()
        {
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Referer", "https://www.bilibili.com/");
        }
        
        public void SetCookie(string cookie)
        {
            _httpClient.DefaultRequestHeaders.Remove("Cookie");
            if (!string.IsNullOrEmpty(cookie))
            {
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
            }
        }
        
        public async Task<DownloadTask> StartDownloadAsync(VideoInfo videoInfo, VideoQuality quality, DownloadItem downloadItem)
        {
            var task = new DownloadTask
            {
                VideoInfo = videoInfo,
                Quality = quality,
                DownloadItem = downloadItem,
                CancellationTokenSource = new CancellationTokenSource()
            };
            
            _activeTasks.Add(task);
            
            try
            {
                await DownloadVideoAsync(task);
            }
            catch (Exception ex)
            {
                downloadItem.Status = "下载失败";
                downloadItem.Progress = 0;
                downloadItem.Speed = "—";
                OnDownloadCompleted(new DownloadCompletedEventArgs { Task = task, Success = false, Error = ex.Message });
            }
            finally
            {
                _activeTasks.Remove(task);
            }
            
            return task;
        }
        
        private async Task DownloadVideoAsync(DownloadTask task)
        {
            var videoInfo = task.VideoInfo;
            var quality = task.Quality;
            var downloadItem = task.DownloadItem;
            var cancellationToken = task.CancellationTokenSource.Token;
            
            downloadItem.Status = "下载中";
            
            // 为每个视频创建专用文件夹
            var videoFolderName = videoInfo.GetSafeFolderName();
            var videoFolderPath = Path.Combine(_downloadDirectory, videoFolderName);

            // 确保文件夹存在
            if (!Directory.Exists(videoFolderPath))
            {
                Directory.CreateDirectory(videoFolderPath);
            }

            // 创建文件名，包含分P信息
            var safeTitle = GetSafeFileName(videoInfo.Title);
            var pageInfo = videoInfo.CurrentPage > 1 ? $"P{videoInfo.CurrentPage}_" : "";
            var videoFileName = $"{pageInfo}{safeTitle}_{quality.QualityName}.mp4";
            var videoFilePath = Path.Combine(videoFolderPath, videoFileName);
            
            downloadItem.SavePath = videoFilePath;
            
            try
            {
                if (!string.IsNullOrEmpty(quality.AudioUrl))
                {
                    // DASH格式：需要分别下载视频和音频，然后合并
                    await DownloadDashVideoAsync(task, videoFilePath);
                }
                else
                {
                    // 传统格式：直接下载
                    await DownloadSingleFileAsync(task, quality.VideoUrl, videoFilePath);

                    // 单文件下载完成
                    downloadItem.Status = "完成";
                    downloadItem.Progress = 100;
                    downloadItem.Speed = "—";

                    // 读取实际文件大小
                    if (File.Exists(videoFilePath))
                    {
                        var fileInfo = new FileInfo(videoFilePath);
                        downloadItem.Size = FormatFileSize(fileInfo.Length);
                    }

                    OnDownloadCompleted(new DownloadCompletedEventArgs { Task = task, Success = true });
                }
            }
            catch (OperationCanceledException)
            {
                downloadItem.Status = "已取消";
                downloadItem.Progress = 0;
                downloadItem.Speed = "—";
                throw;
            }
            catch (Exception ex)
            {
                downloadItem.Status = "下载失败";
                downloadItem.Progress = 0;
                downloadItem.Speed = "—";
                throw new Exception($"视频下载失败: {ex.Message}", ex);
            }
        }
        
        private async Task DownloadDashVideoAsync(DownloadTask task, string outputPath)
        {
            var quality = task.Quality;
            var downloadItem = task.DownloadItem;
            var cancellationToken = task.CancellationTokenSource.Token;
            
            var tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDir);
            
            try
            {
                var videoTempPath = Path.Combine(tempDir, "video.m4s");
                var audioTempPath = Path.Combine(tempDir, "audio.m4s");

                // 下载视频流
                downloadItem.Status = "下载视频流";
                await DownloadSingleFileAsync(task, quality.VideoUrl, videoTempPath, 0.7);

                // 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 下载音频流
                downloadItem.Status = "下载音频流";
                await DownloadSingleFileAsync(task, quality.AudioUrl, audioTempPath, 0.3);

                // 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 合并音视频（这里需要FFmpeg，暂时先复制视频文件）
                downloadItem.Status = "合并音视频";
                File.Copy(videoTempPath, outputPath, true);

                downloadItem.Status = "完成";
                downloadItem.Progress = 100;
                downloadItem.Speed = "—";

                // 读取实际文件大小
                if (File.Exists(outputPath))
                {
                    var fileInfo = new FileInfo(outputPath);
                    downloadItem.Size = FormatFileSize(fileInfo.Length);
                }

                OnDownloadCompleted(new DownloadCompletedEventArgs { Task = task, Success = true });
            }
            catch (OperationCanceledException)
            {
                downloadItem.Status = "已取消";
                downloadItem.Progress = 0;
                downloadItem.Speed = "—";
                throw;
            }
            catch (Exception ex)
            {
                downloadItem.Status = "下载失败";
                downloadItem.Progress = 0;
                downloadItem.Speed = "—";
                throw new Exception($"DASH视频下载失败: {ex.Message}", ex);
            }
            finally
            {
                // 清理临时文件
                if (Directory.Exists(tempDir))
                {
                    try
                    {
                        Directory.Delete(tempDir, true);
                    }
                    catch
                    {
                        // 忽略清理失败
                    }
                }
            }
        }
        
        private async Task DownloadSingleFileAsync(DownloadTask task, string url, string filePath, double progressWeight = 1.0)
        {
            var downloadItem = task.DownloadItem;
            var cancellationToken = task.CancellationTokenSource.Token;
            
            using var response = await _httpClient.GetAsync(url, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            response.EnsureSuccessStatusCode();

            var totalBytes = response.Content.Headers.ContentLength ?? 0;
            var downloadedBytes = 0L;

            // 初始化显示大小
            if (totalBytes > 0)
            {
                if (progressWeight == 1.0) // 单文件下载显示总大小
                {
                    downloadItem.Size = FormatFileSize(totalBytes);
                }
                else // DASH下载显示当前流大小
                {
                    downloadItem.Size = FormatFileSize(0); // 初始为0，下载过程中更新
                }
            }
            else
            {
                downloadItem.Size = "未知大小";
            }
            
            using var contentStream = await response.Content.ReadAsStreamAsync();
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);
            
            var buffer = new byte[8192];
            var lastProgressUpdate = DateTime.Now;
            
            while (true)
            {
                // 检查暂停状态
                task.PauseEvent.Wait(cancellationToken);
                cancellationToken.ThrowIfCancellationRequested();

                var bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                if (bytesRead == 0) break;

                await fileStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
                downloadedBytes += bytesRead;
                
                // 更新进度（限制更新频率）
                var now = DateTime.Now;
                if ((now - lastProgressUpdate).TotalMilliseconds > 500)
                {
                    var currentProgress = totalBytes > 0 ? (double)downloadedBytes / totalBytes * 100 : 0;

                    // 在UI线程上更新进度
                    _dispatcher.Invoke(() =>
                    {
                        // 对于DASH下载，需要累加进度
                        if (progressWeight < 1.0)
                        {
                            // 这是DASH下载的一部分，按权重计算进度
                            if (progressWeight == 0.7) // 视频流，占70%
                            {
                                var videoProgress = Math.Min(currentProgress * 0.7, 70);
                                downloadItem.Progress = videoProgress;
                            }
                            else if (progressWeight == 0.3) // 音频流，占30%
                            {
                                // 音频流进度从70%开始，最多到100%
                                var audioProgress = Math.Min(currentProgress * 0.3, 30);
                                downloadItem.Progress = 70 + audioProgress;
                            }
                        }
                        else
                        {
                            // 单文件下载
                            downloadItem.Progress = Math.Min(currentProgress, 100);
                        }
                    });

                    // 在UI线程上更新大小和速度显示
                    _dispatcher.Invoke(() =>
                    {
                        // 更新大小显示
                        if (progressWeight < 1.0)
                        {
                            // DASH下载：累加显示总下载大小
                            if (progressWeight == 0.7) // 视频流
                            {
                                task.TotalDownloadedBytes = downloadedBytes; // 记录视频流大小
                                downloadItem.Size = FormatFileSize(downloadedBytes);
                            }
                            else if (progressWeight == 0.3) // 音频流
                            {
                                // 累加音频流大小到总大小
                                var totalDownloadedSize = task.TotalDownloadedBytes + downloadedBytes;
                                downloadItem.Size = FormatFileSize(totalDownloadedSize);
                            }
                        }
                        else
                        {
                            // 单文件下载
                            downloadItem.Size = FormatFileSize(downloadedBytes);
                        }

                        var speed = CalculateSpeed(downloadedBytes, DateTime.Now - task.StartTime);
                        downloadItem.Speed = speed;
                    });

                    OnProgressChanged(new DownloadProgressEventArgs { Task = task });
                    lastProgressUpdate = now;
                }
            }
        }
        
        private string GetSafeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            foreach (var c in invalidChars)
            {
                fileName = fileName.Replace(c, '_');
            }
            return fileName.Length > 100 ? fileName.Substring(0, 100) : fileName;
        }
        
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
        
        private string CalculateSpeed(long downloadedBytes, TimeSpan elapsed)
        {
            if (elapsed.TotalSeconds < 1) return "0 KB/s";
            
            var bytesPerSecond = downloadedBytes / elapsed.TotalSeconds;
            return FormatFileSize((long)bytesPerSecond) + "/s";
        }
        
        public void CancelDownload(DownloadTask task)
        {
            task?.CancellationTokenSource?.Cancel();
        }

        public void CancelAllDownloads()
        {
            foreach (var task in _activeTasks.ToArray())
            {
                CancelDownload(task);
            }
        }

        public void PauseDownload(DownloadTask task)
        {
            if (task != null && !task.IsPaused)
            {
                task.IsPaused = true;
                task.PauseEvent.Reset(); // 暂停下载
                task.DownloadItem.Status = "已暂停";
                task.DownloadItem.Speed = "—";
            }
        }

        public void ResumeDownload(DownloadTask task)
        {
            if (task != null && task.IsPaused)
            {
                task.IsPaused = false;
                task.PauseEvent.Set(); // 恢复下载
                task.DownloadItem.Status = "下载中";
            }
        }

        public void PauseAllDownloads()
        {
            foreach (var task in _activeTasks.ToArray())
            {
                PauseDownload(task);
            }
        }

        public void ResumeAllDownloads()
        {
            foreach (var task in _activeTasks.ToArray())
            {
                ResumeDownload(task);
            }
        }

        public DownloadTask FindTaskByDownloadItem(DownloadItem downloadItem)
        {
            return _activeTasks.FirstOrDefault(t => t.DownloadItem == downloadItem);
        }
        
        protected virtual void OnProgressChanged(DownloadProgressEventArgs e)
        {
            ProgressChanged?.Invoke(this, e);
        }
        
        protected virtual void OnDownloadCompleted(DownloadCompletedEventArgs e)
        {
            DownloadCompleted?.Invoke(this, e);
        }
        
        public void Dispose()
        {
            CancelAllDownloads();
            _httpClient?.Dispose();
        }
    }
    
    public class DownloadTask
    {
        public VideoInfo VideoInfo { get; set; }
        public VideoQuality Quality { get; set; }
        public DownloadItem DownloadItem { get; set; }
        public CancellationTokenSource CancellationTokenSource { get; set; }
        public DateTime StartTime { get; set; } = DateTime.Now;
        public long TotalDownloadedBytes { get; set; } = 0; // 用于DASH下载累计大小
        public bool IsPaused { get; set; } = false;
        public ManualResetEventSlim PauseEvent { get; set; } = new ManualResetEventSlim(true); // 默认为非暂停状态
        public long DownloadedBytesBeforePause { get; set; } = 0; // 暂停前已下载的字节数
        public string TempFilePath { get; set; } // 临时文件路径，用于断点续传
    }
    
    public class DownloadProgressEventArgs : EventArgs
    {
        public DownloadTask Task { get; set; }
    }
    
    public class DownloadCompletedEventArgs : EventArgs
    {
        public DownloadTask Task { get; set; }
        public bool Success { get; set; }
        public string Error { get; set; }
    }
}
