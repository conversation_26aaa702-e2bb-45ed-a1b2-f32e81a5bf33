﻿using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using BilibiliDownloader.Core;
using BilibiliDownloader.Models;
using BilibiliDownloader.UI;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader
{
    public partial class MainWindow : Window
    {
        private AccountManager _accountManager;
        private BilibiliApi _bilibiliApi;
        private DownloadManager _downloadManager;
        private ObservableCollection<DownloadItem> _downloadItems;
        private VideoInfo _currentVideoInfo;
        private bool _isUpdatingSelection = false; // 防止无限循环的标志

        public MainWindow()
        {
            InitializeComponent();
            InitializeComponents();
            SetupEventHandlers();
        }

        private void InitializeComponents()
        {
            _accountManager = new AccountManager();
            _bilibiliApi = new BilibiliApi();
            _downloadManager = new DownloadManager();
            _downloadItems = new ObservableCollection<DownloadItem>();

            lvDownloads.ItemsSource = _downloadItems;

            // 设置当前账号显示
            UpdateCurrentAccountDisplay();

            // 订阅账号变更事件
            _accountManager.AccountChanged += OnAccountChanged;

            // 订阅下载事件
            _downloadManager.ProgressChanged += OnDownloadProgressChanged;
            _downloadManager.DownloadCompleted += OnDownloadCompleted;

            // 订阅日志事件
            Utils.Logger.Instance.LogMessageReceived += OnLogMessageReceived;

            // 清理旧日志
            Utils.Logger.Instance.ClearOldLogs();

            Utils.Logger.Instance.Info("程序初始化完成");
            LogMessage("程序初始化完成");
        }

        private void SetupEventHandlers()
        {
            Closing += MainWindow_Closing;
        }

        private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            _downloadManager?.Dispose();
            _bilibiliApi?.Dispose();
        }

        private void UpdateCurrentAccountDisplay()
        {
            var currentAccount = _accountManager.GetCurrentAccount();
            if (currentAccount != null)
            {
                txtCurrentUser.Text = $"已登录: {currentAccount.Username}";

                // 设置API的Cookie
                if (!string.IsNullOrEmpty(currentAccount.Cookie))
                {
                    _bilibiliApi.SetCookie(currentAccount.Cookie);
                    _downloadManager.SetCookie(currentAccount.Cookie);
                }
            }
            else
            {
                txtCurrentUser.Text = "未登录";
            }
        }

        private void OnAccountChanged(object sender, AccountInfo account)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateCurrentAccountDisplay();
                LogMessage(account != null ? $"已切换到账号: {account.Username}" : "已退出登录");
            });
        }

        private void BtnAccountManager_Click(object sender, RoutedEventArgs e)
        {
            var accountWindow = new AccountManagerWindow(_accountManager)
            {
                Owner = this
            };
            accountWindow.ShowDialog();
        }

        private async void BtnAnalyze_Click(object sender, RoutedEventArgs e)
        {
            var url = txtVideoUrl.Text.Trim();
            if (string.IsNullOrEmpty(url))
            {
                MessageBox.Show("请输入视频链接", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            btnAnalyze.IsEnabled = false;
            txtStatus.Text = "正在解析视频信息...";

            try
            {
                LogMessage($"开始解析视频: {url}");
                _currentVideoInfo = await _bilibiliApi.GetVideoInfoAsync(url);

                DisplayVideoInfo(_currentVideoInfo);
                PopulateQualityOptions(_currentVideoInfo);

                btnDownload.IsEnabled = true;
                txtStatus.Text = "视频解析完成";
                LogMessage("视频解析成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"解析视频失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                txtStatus.Text = "解析失败";
                LogMessage($"解析失败: {ex.Message}");
            }
            finally
            {
                btnAnalyze.IsEnabled = true;
            }
        }

        private void DisplayVideoInfo(VideoInfo videoInfo)
        {
            pnlVideoInfo.Children.Clear();

            AddInfoItem("标题", videoInfo.Title);
            AddInfoItem("作者", videoInfo.Author);
            AddInfoItem("时长", TimeSpan.FromSeconds(videoInfo.Duration).ToString(@"hh\:mm\:ss"));
            AddInfoItem("发布时间", videoInfo.PublishTime.ToString("yyyy-MM-dd"));

            if (videoInfo.IsPaymentRequired)
            {
                AddInfoItem("付费状态", "需要付费", true);
            }

            if (videoInfo.IsVip)
            {
                AddInfoItem("会员状态", "需要大会员", true);
            }

            if (videoInfo.IsCollection)
            {
                AddInfoItem("合集状态", $"共{videoInfo.Pages.Count}个视频", false);

                // 显示当前选择的页面信息
                var currentPage = videoInfo.Pages.FirstOrDefault(p => p.Page == videoInfo.CurrentPage);
                if (currentPage != null)
                {
                    AddInfoItem("当前页面", $"P{currentPage.Page}: {currentPage.Part}", false);
                    AddInfoItem("页面时长", TimeSpan.FromSeconds(currentPage.Duration).ToString(@"hh\:mm\:ss"));
                }
                else
                {
                    AddInfoItem("当前页面", $"P{videoInfo.CurrentPage}", false);
                }

                // 显示合集视频列表
                ShowSeriesList(videoInfo);
            }
            else
            {
                // 隐藏合集列表
                expSeriesList.Visibility = Visibility.Collapsed;
            }

            AddInfoItem("可用清晰度", $"{videoInfo.AvailableQualities.Count} 种");
        }

        private void AddInfoItem(string label, string value, bool isWarning = false)
        {
            var panel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 2, 0, 2) };

            var labelBlock = new TextBlock
            {
                Text = label + ": ",
                FontWeight = FontWeights.Bold,
                Width = 80
            };

            var valueBlock = new TextBlock
            {
                Text = value,
                TextWrapping = TextWrapping.Wrap,
                Foreground = isWarning ? System.Windows.Media.Brushes.Red : System.Windows.Media.Brushes.Black
            };

            panel.Children.Add(labelBlock);
            panel.Children.Add(valueBlock);
            pnlVideoInfo.Children.Add(panel);
        }

        private void PopulateQualityOptions(VideoInfo videoInfo)
        {
            cmbQuality.Items.Clear();

            LogMessage($"开始填充清晰度选项，可用清晰度数量: {videoInfo.AvailableQualities.Count}");
            Utils.Logger.Instance.Info($"填充清晰度选项: {videoInfo.AvailableQualities.Count} 个选项");

            if (videoInfo.AvailableQualities.Count == 0)
            {
                LogMessage("警告：没有找到可用的清晰度选项");
                Utils.Logger.Instance.Warning("没有找到可用的清晰度选项");

                // 添加一个默认选项提示用户
                cmbQuality.Items.Add("无可用清晰度");
                cmbQuality.SelectedIndex = 0;
                cmbQuality.IsEnabled = false;
                return;
            }

            foreach (var quality in videoInfo.AvailableQualities.OrderByDescending(q => q.Quality))
            {
                LogMessage($"添加清晰度选项: {quality.QualityName} ({quality.Quality})");
                cmbQuality.Items.Add(quality);
            }

            if (cmbQuality.Items.Count > 0)
            {
                cmbQuality.SelectedIndex = 0;
                cmbQuality.IsEnabled = true;
                LogMessage($"清晰度选项填充完成，默认选择: {((VideoQuality)cmbQuality.SelectedItem).QualityName}");
            }
        }

        private async void BtnDownload_Click(object sender, RoutedEventArgs e)
        {
            if (_currentVideoInfo == null)
            {
                MessageBox.Show("请先解析视频信息", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedQuality = cmbQuality.SelectedItem as VideoQuality;
            if (selectedQuality == null)
            {
                MessageBox.Show("请选择下载清晰度", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 检查付费绕过选项
            if (chkBypassPayment.IsChecked == true && _currentVideoInfo.IsPaymentRequired)
            {
                var result = MessageBox.Show("您选择了绕过付费检测，这是用于安全测试的功能。\n确定要继续吗？",
                                           "安全测试确认", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result != MessageBoxResult.Yes)
                {
                    return;
                }

                LogMessage("启用付费绕过模式 - 安全测试");
            }

            if (chkDownloadSeries.IsChecked == true && _currentVideoInfo.IsCollection)
            {
                await DownloadSeriesAsync();
            }
            else
            {
                await DownloadSingleVideoAsync(_currentVideoInfo, selectedQuality);
            }
        }

        private async Task DownloadSingleVideoAsync(VideoInfo videoInfo, VideoQuality quality)
        {
            var downloadItem = new DownloadItem
            {
                VideoId = videoInfo.VideoId,
                Title = videoInfo.Title,
                Url = videoInfo.OriginalUrl
            };

            _downloadItems.Add(downloadItem);
            LogMessage($"开始下载: {videoInfo.Title}");

            try
            {
                await _downloadManager.StartDownloadAsync(videoInfo, quality, downloadItem);
            }
            catch (Exception ex)
            {
                LogMessage($"下载失败: {ex.Message}");
            }
        }

        private async Task DownloadSeriesAsync()
        {
            if (_currentVideoInfo == null || !_currentVideoInfo.IsCollection)
            {
                MessageBox.Show("当前视频不是合集", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedQuality = cmbQuality.SelectedItem as VideoQuality;
            if (selectedQuality == null)
            {
                MessageBox.Show("请选择下载清晰度", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"确定要下载整个合集吗？\n合集包含 {_currentVideoInfo.Pages?.Count ?? 0} 个视频。",
                                       "确认下载", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result != MessageBoxResult.Yes)
            {
                return;
            }

            LogMessage($"开始下载合集: {_currentVideoInfo.Title}");

            var successCount = 0;
            var failCount = 0;
            var failedPages = new List<VideoPage>();

            foreach (var page in _currentVideoInfo.Pages)
            {
                try
                {
                    LogMessage($"正在下载 P{page.Page}: {page.Part}");

                    // 为每个分P创建单独的VideoInfo
                    var pageVideoInfo = new VideoInfo
                    {
                        VideoId = _currentVideoInfo.VideoId,
                        Title = page.Part,
                        OriginalUrl = _currentVideoInfo.OriginalUrl,
                        CurrentPage = page.Page,
                        IsCollection = false,
                        Pages = null
                    };

                    // 获取该分P的播放信息
                    var pagePlayInfo = await _bilibiliApi.GetVideoPlayInfoAsync(_currentVideoInfo.VideoId, page.Page);
                    if (pagePlayInfo?.data?.dash?.video != null)
                    {
                        // 找到对应清晰度的视频流
                        var videoStreams = pagePlayInfo.data.dash.video as Newtonsoft.Json.Linq.JArray;
                        var audioStreams = pagePlayInfo.data.dash.audio as Newtonsoft.Json.Linq.JArray;

                        if (videoStreams?.Count > 0)
                        {
                            var videoStream = videoStreams[0];
                            var audioStream = audioStreams?.FirstOrDefault();

                            var pageQuality = new VideoQuality
                            {
                                QualityName = selectedQuality.QualityName,
                                VideoUrl = videoStream["baseUrl"]?.ToString() ?? videoStream["base_url"]?.ToString(),
                                AudioUrl = audioStream?["baseUrl"]?.ToString() ?? audioStream?["base_url"]?.ToString()
                            };

                            try
                            {
                                await DownloadSingleVideoAsync(pageVideoInfo, pageQuality);
                                successCount++;
                            }
                            catch (Exception downloadEx)
                            {
                                LogMessage($"P{page.Page} 下载失败: {downloadEx.Message}");
                                failCount++;
                                failedPages.Add(page);
                            }
                        }
                        else
                        {
                            LogMessage($"P{page.Page} 无可用视频流，跳过");
                            failCount++;
                            failedPages.Add(page);
                        }
                    }
                    else
                    {
                        LogMessage($"P{page.Page} 获取播放信息失败，可能需要权限或付费，跳过");
                        failCount++;
                        failedPages.Add(page);
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"P{page.Page} 下载失败: {ex.Message}");
                    failCount++;
                    failedPages.Add(page);
                }

                // 添加延迟避免请求过快
                await Task.Delay(1000);
            }

            // 如果有失败的视频，尝试重试
            if (failedPages.Count > 0)
            {
                LogMessage($"开始重试失败的 {failedPages.Count} 个视频...");

                var retrySuccessCount = 0;
                var retryFailCount = 0;

                foreach (var page in failedPages)
                {
                    try
                    {
                        LogMessage($"重试下载 P{page.Page}: {page.Part}");

                        // 为重试的分P创建单独的VideoInfo
                        var pageVideoInfo = new VideoInfo
                        {
                            VideoId = _currentVideoInfo.VideoId,
                            Title = page.Part,
                            OriginalUrl = _currentVideoInfo.OriginalUrl,
                            CurrentPage = page.Page,
                            IsCollection = false,
                            Pages = null
                        };

                        // 重新获取播放信息
                        var pagePlayInfo = await _bilibiliApi.GetVideoPlayInfoAsync(_currentVideoInfo.VideoId, page.Page);
                        if (pagePlayInfo?.data?.dash?.video != null)
                        {
                            var videoStreams = pagePlayInfo.data.dash.video as Newtonsoft.Json.Linq.JArray;
                            var audioStreams = pagePlayInfo.data.dash.audio as Newtonsoft.Json.Linq.JArray;

                            if (videoStreams?.Count > 0)
                            {
                                var videoStream = videoStreams[0];
                                var audioStream = audioStreams?.FirstOrDefault();

                                var pageQuality = new VideoQuality
                                {
                                    QualityName = selectedQuality.QualityName,
                                    VideoUrl = videoStream["baseUrl"]?.ToString() ?? videoStream["base_url"]?.ToString(),
                                    AudioUrl = audioStream?["baseUrl"]?.ToString() ?? audioStream?["base_url"]?.ToString()
                                };

                                await DownloadSingleVideoAsync(pageVideoInfo, pageQuality);
                                retrySuccessCount++;
                                successCount++;
                                failCount--;
                            }
                            else
                            {
                                retryFailCount++;
                            }
                        }
                        else
                        {
                            retryFailCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"P{page.Page} 重试失败: {ex.Message}");
                        retryFailCount++;
                    }

                    // 重试间隔稍长一些
                    await Task.Delay(2000);
                }

                LogMessage($"重试完成: 成功 {retrySuccessCount} 个，仍失败 {retryFailCount} 个");
            }

            LogMessage($"合集下载完成: 成功 {successCount} 个，失败 {failCount} 个");
        }

        private void OnDownloadProgressChanged(object sender, DownloadProgressEventArgs e)
        {
            // 进度更新已通过数据绑定自动处理
            Dispatcher.Invoke(() =>
            {
                UpdateTotalSpeed();
            });
        }

        private void OnDownloadCompleted(object sender, DownloadCompletedEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                if (e.Success)
                {
                    LogMessage($"下载完成: {e.Task.VideoInfo.Title}");
                }
                else
                {
                    LogMessage($"下载失败: {e.Task.VideoInfo.Title} - {e.Error}");
                }

                UpdateTotalSpeed();
            });
        }

        private void UpdateTotalSpeed()
        {
            var activeDownloads = _downloadItems.Count(d => d.Status == "下载中");
            txtActiveDownloads.Text = activeDownloads.ToString();

            // 计算总下载速度（简化实现）
            var totalSpeed = _downloadItems
                .Where(d => d.Status == "下载中")
                .Sum(d => ParseSpeed(d.Speed));

            txtTotalSpeed.Text = FormatSpeed(totalSpeed);
        }

        private double ParseSpeed(string speedText)
        {
            if (string.IsNullOrEmpty(speedText) || !speedText.Contains("/s"))
                return 0;

            var parts = speedText.Replace("/s", "").Split(' ');
            if (parts.Length != 2 || !double.TryParse(parts[0], out var value))
                return 0;

            return parts[1].ToUpper() switch
            {
                "KB" => value,
                "MB" => value * 1024,
                "GB" => value * 1024 * 1024,
                _ => value
            };
        }

        private string FormatSpeed(double kbps)
        {
            if (kbps < 1024)
                return $"{kbps:F1} KB/s";
            else if (kbps < 1024 * 1024)
                return $"{kbps / 1024:F1} MB/s";
            else
                return $"{kbps / (1024 * 1024):F1} GB/s";
        }

        private void BtnPauseAll_Click(object sender, RoutedEventArgs e)
        {
            LogMessage("暂停功能将在后续版本中实现");
        }

        private void BtnResumeAll_Click(object sender, RoutedEventArgs e)
        {
            LogMessage("继续功能将在后续版本中实现");
        }

        private void BtnClearCompleted_Click(object sender, RoutedEventArgs e)
        {
            var completedItems = _downloadItems.Where(d => d.Status == "完成").ToList();
            foreach (var item in completedItems)
            {
                _downloadItems.Remove(item);
            }
            LogMessage($"已清除 {completedItems.Count} 个已完成的下载项");
        }

        private void OnLogMessageReceived(object sender, Utils.LogEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                var timestamp = e.Timestamp.ToString("HH:mm:ss");
                var levelColor = e.Level switch
                {
                    Utils.LogLevel.Error => "Red",
                    Utils.LogLevel.Warning => "Orange",
                    Utils.LogLevel.Info => "Lime",
                    Utils.LogLevel.Debug => "Gray",
                    _ => "Lime"
                };

                // 这里可以根据日志级别设置不同颜色，但由于TextBox限制，暂时使用统一格式
                var logText = $"[{timestamp}] [{e.Level}] {e.Message}";
                if (e.Exception != null)
                {
                    logText += $" - {e.Exception.Message}";
                }

                txtLog.AppendText(logText + "\n");
                svLog.ScrollToEnd();
            });
        }

        private void ShowSeriesList(VideoInfo videoInfo)
        {
            _isUpdatingSelection = true; // 设置标志，防止触发选择事件

            lbSeriesVideos.ItemsSource = videoInfo.Pages;
            expSeriesList.Visibility = Visibility.Visible;

            // 选中当前页面
            var currentPage = videoInfo.Pages.FirstOrDefault(p => p.Page == videoInfo.CurrentPage);
            if (currentPage != null)
            {
                lbSeriesVideos.SelectedItem = currentPage;
            }

            _isUpdatingSelection = false; // 重置标志
        }

        private async void LbSeriesVideos_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 如果正在更新选择，忽略此事件
            if (_isUpdatingSelection) return;

            if (lbSeriesVideos.SelectedItem is VideoPage selectedPage && _currentVideoInfo != null)
            {
                // 如果选择的就是当前页面，不需要重新解析
                if (selectedPage.Page == _currentVideoInfo.CurrentPage)
                {
                    return;
                }

                try
                {
                    LogMessage($"切换到P{selectedPage.Page}: {selectedPage.Part}");
                    Utils.Logger.Instance.Info($"用户选择合集视频: P{selectedPage.Page}");

                    // 直接更新当前视频信息，而不是重新解析整个视频
                    await SwitchToSeriesPage(selectedPage);
                }
                catch (Exception ex)
                {
                    LogMessage($"切换视频失败: {ex.Message}");
                    Utils.Logger.Instance.Error("切换合集视频失败", ex);
                }
            }
        }

        private async Task SwitchToSeriesPage(VideoPage selectedPage)
        {
            if (_currentVideoInfo == null) return;

            btnAnalyze.IsEnabled = false;
            txtStatus.Text = $"正在切换到P{selectedPage.Page}...";

            try
            {
                // 构造新的URL
                var baseUrl = _currentVideoInfo.OriginalUrl.Split('?')[0];
                var newUrl = $"{baseUrl}?p={selectedPage.Page}";

                // 更新URL输入框
                txtVideoUrl.Text = newUrl;

                // 重新解析视频（但不会触发选择事件，因为我们有_isUpdatingSelection标志）
                _isUpdatingSelection = true; // 防止重新解析时触发选择事件

                _currentVideoInfo = await _bilibiliApi.GetVideoInfoAsync(newUrl);
                DisplayVideoInfo(_currentVideoInfo);
                PopulateQualityOptions(_currentVideoInfo);

                _isUpdatingSelection = false; // 重置标志

                btnDownload.IsEnabled = true;
                txtStatus.Text = $"已切换到P{selectedPage.Page}: {selectedPage.Part}";

                LogMessage($"成功切换到P{selectedPage.Page}: {selectedPage.Part}");
            }
            catch (Exception ex)
            {
                _isUpdatingSelection = false; // 确保重置标志
                MessageBox.Show($"切换视频失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                txtStatus.Text = "切换失败";
                throw;
            }
            finally
            {
                btnAnalyze.IsEnabled = true;
            }
        }



        private async Task ReAnalyzeCurrentVideo()
        {
            var url = txtVideoUrl.Text.Trim();
            if (string.IsNullOrEmpty(url)) return;

            btnAnalyze.IsEnabled = false;
            txtStatus.Text = "正在重新解析视频...";

            try
            {
                _currentVideoInfo = await _bilibiliApi.GetVideoInfoAsync(url);
                DisplayVideoInfo(_currentVideoInfo);
                PopulateQualityOptions(_currentVideoInfo);
                btnDownload.IsEnabled = true;
                txtStatus.Text = "视频重新解析完成";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重新解析视频失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                txtStatus.Text = "重新解析失败";
            }
            finally
            {
                btnAnalyze.IsEnabled = true;
            }
        }

        private void LogMessage(string message)
        {
            Dispatcher.Invoke(() =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                txtLog.AppendText($"[{timestamp}] {message}\n");
                svLog.ScrollToEnd();
            });
        }

        // 双击播放视频
        private void LvDownloads_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (lvDownloads.SelectedItem is DownloadItem selectedItem)
            {
                OpenVideo(selectedItem);
            }
        }

        // 右键菜单：打开视频
        private void MenuItem_OpenVideo_Click(object sender, RoutedEventArgs e)
        {
            if (lvDownloads.SelectedItem is DownloadItem selectedItem)
            {
                OpenVideo(selectedItem);
            }
        }

        // 右键菜单：打开所在文件夹
        private void MenuItem_OpenFolder_Click(object sender, RoutedEventArgs e)
        {
            if (lvDownloads.SelectedItem is DownloadItem selectedItem)
            {
                OpenFolder(selectedItem);
            }
        }

        // 右键菜单：复制文件路径
        private void MenuItem_CopyPath_Click(object sender, RoutedEventArgs e)
        {
            if (lvDownloads.SelectedItem is DownloadItem selectedItem)
            {
                CopyPath(selectedItem);
            }
        }

        // 右键菜单：删除下载项
        private void MenuItem_RemoveItem_Click(object sender, RoutedEventArgs e)
        {
            if (lvDownloads.SelectedItem is DownloadItem selectedItem)
            {
                RemoveDownloadItem(selectedItem);
            }
        }

        private void OpenVideo(DownloadItem item)
        {
            if (string.IsNullOrEmpty(item.SavePath) || !File.Exists(item.SavePath))
            {
                MessageBox.Show("视频文件不存在或尚未下载完成", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = item.SavePath,
                    UseShellExecute = true
                });
                LogMessage($"正在打开视频: {item.Title}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开视频文件: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                LogMessage($"打开视频失败: {ex.Message}");
            }
        }

        private void OpenFolder(DownloadItem item)
        {
            if (string.IsNullOrEmpty(item.SavePath))
            {
                MessageBox.Show("文件路径不存在", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var folderPath = Path.GetDirectoryName(item.SavePath);
                if (Directory.Exists(folderPath))
                {
                    // 如果文件存在，选中文件；否则只打开文件夹
                    if (File.Exists(item.SavePath))
                    {
                        Process.Start("explorer.exe", $"/select,\"{item.SavePath}\"");
                    }
                    else
                    {
                        Process.Start("explorer.exe", folderPath);
                    }
                    LogMessage($"正在打开文件夹: {folderPath}");
                }
                else
                {
                    MessageBox.Show("文件夹不存在", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开文件夹: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                LogMessage($"打开文件夹失败: {ex.Message}");
            }
        }

        private void CopyPath(DownloadItem item)
        {
            if (string.IsNullOrEmpty(item.SavePath))
            {
                MessageBox.Show("文件路径不存在", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                Clipboard.SetText(item.SavePath);
                LogMessage($"已复制文件路径: {item.SavePath}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制路径失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RemoveDownloadItem(DownloadItem item)
        {
            var result = MessageBox.Show($"确定要删除下载项 \"{item.Title}\" 吗？\n注意：这不会删除已下载的文件。",
                                       "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                _downloadItems.Remove(item);
                LogMessage($"已删除下载项: {item.Title}");
            }
        }
    }
}