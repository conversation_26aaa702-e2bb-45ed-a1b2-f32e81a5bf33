<Window x:Class="BilibiliDownloader.UI.AccountManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BilibiliDownloader.UI"
        mc:Ignorable="d"
        Title="账号管理" Height="500" Width="600" 
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF2B2B2B" Padding="15">
            <TextBlock Text="Bilibili账号管理" Foreground="White" FontSize="16" FontWeight="Bold"/>
        </Border>
        
        <!-- 主要内容 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧：账号列表 -->
            <GroupBox Grid.Column="0" Header="已保存的账号" Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <ListBox Name="lbAccounts" Grid.Row="0" SelectionChanged="LbAccounts_SelectionChanged">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel>
                                    <TextBlock Text="{Binding Username}" FontWeight="Bold"/>
                                    <TextBlock Text="{Binding LastLoginTime}" FontSize="10" Foreground="Gray"/>
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                    
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,10,0,0">
                        <Button Name="btnDeleteAccount" Content="删除账号" Padding="10,5" Click="BtnDeleteAccount_Click" IsEnabled="False"/>
                        <Button Name="btnUseAccount" Content="使用此账号" Margin="10,0,0,0" Padding="10,5" Click="BtnUseAccount_Click" IsEnabled="False"/>
                    </StackPanel>
                </Grid>
            </GroupBox>
            
            <!-- 右侧：添加/编辑账号 -->
            <GroupBox Grid.Column="1" Header="添加新账号" Margin="10,0,0,0">
                <StackPanel>
                    <TextBlock Text="用户名/邮箱/手机号:" Margin="0,0,0,5"/>
                    <TextBox Name="txtUsername" Height="30" VerticalContentAlignment="Center"/>
                    
                    <TextBlock Text="密码:" Margin="0,15,0,5"/>
                    <PasswordBox Name="txtPassword" Height="30" VerticalContentAlignment="Center"/>
                    
                    <CheckBox Name="chkSavePassword" Content="保存密码到本地" Margin="0,10,0,0" IsChecked="True"/>
                    <TextBlock Text="注意：密码将以明文形式保存在本地文件中" 
                               Foreground="Red" FontSize="10" Margin="0,5,0,0" TextWrapping="Wrap"/>
                    
                    <Separator Margin="0,15,0,15"/>
                    
                    <Button Name="btnAddAccount" Content="添加账号" Padding="15,8" Click="BtnAddAccount_Click"/>
                    <Button Name="btnTestLogin" Content="测试登录" Margin="0,10,0,0" Padding="15,8" Click="BtnTestLogin_Click"/>
                    
                    <Separator Margin="0,15,0,15"/>
                    
                    <TextBlock Text="Cookie登录 (高级):" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox Name="txtCookie" Height="60" TextWrapping="Wrap" AcceptsReturn="True" 
                             VerticalScrollBarVisibility="Auto" VerticalContentAlignment="Top"/>
                    <Button Name="btnAddCookie" Content="使用Cookie登录" Margin="0,10,0,0" Padding="15,8" Click="BtnAddCookie_Click"/>
                </StackPanel>
            </GroupBox>
        </Grid>
        
        <!-- 底部按钮 -->
        <Border Grid.Row="2" Background="#FFF5F5F5" Padding="15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Name="btnOpenAccountFile" Content="打开账号文件" Padding="15,8" Click="BtnOpenAccountFile_Click"/>
                <Button Name="btnClose" Content="关闭" Margin="10,0,0,0" Padding="15,8" Click="BtnClose_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
