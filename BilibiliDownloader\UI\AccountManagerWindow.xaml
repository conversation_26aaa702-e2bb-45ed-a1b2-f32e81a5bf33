<Window x:Class="BilibiliDownloader.UI.AccountManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BilibiliDownloader.UI"
        mc:Ignorable="d"
        Title="🐱 像素喵 - 多平台账号管理" Height="650" Width="800"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize" MinHeight="650" MinWidth="800">

    <Window.Resources>
        <local:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <local:BoolToStatusConverter x:Key="BoolToStatusConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF2B2B2B" Padding="15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🐱 像素喵多平台账号管理" Foreground="White" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                <TextBlock Text="  |  支持多个视频平台的账号管理" Foreground="#FFAAAAAA" FontSize="12" VerticalAlignment="Center" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- 主要内容 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1.2*"/>
                <ColumnDefinition Width="1.5*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：账号列表 -->
            <GroupBox Grid.Column="0" Header="📋 已保存的账号" Margin="0,0,15,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 平台筛选 -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="🔍 筛选平台:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Name="cmbPlatformFilter" Width="120" SelectionChanged="CmbPlatformFilter_SelectionChanged">
                            <ComboBoxItem Content="全部平台" IsSelected="True"/>
                        </ComboBox>
                    </StackPanel>

                    <ListBox Name="lbAccounts" Grid.Row="1" SelectionChanged="LbAccounts_SelectionChanged">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="#FFDDDDDD" BorderThickness="1" Margin="2" Padding="8" CornerRadius="4">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding PlatformDisplayName}" FontWeight="Bold" Foreground="#FF0066CC"/>
                                            <TextBlock Text="{Binding DisplayName}" FontWeight="Bold" Margin="10,0,0,0"/>
                                        </StackPanel>
                                        <TextBlock Text="{Binding Username}" FontSize="11" Foreground="#FF666666" Margin="0,2,0,0"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                            <TextBlock Text="最后登录: " FontSize="10" Foreground="Gray"/>
                                            <TextBlock Text="{Binding LastLoginTime, StringFormat=yyyy-MM-dd HH:mm}" FontSize="10" Foreground="Gray"/>
                                        </StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                            <Ellipse Width="8" Height="8" VerticalAlignment="Center" Margin="0,0,5,0">
                                                <Ellipse.Fill>
                                                    <SolidColorBrush Color="{Binding IsValid, Converter={StaticResource BoolToColorConverter}}"/>
                                                </Ellipse.Fill>
                                            </Ellipse>
                                            <TextBlock Text="{Binding IsValid, Converter={StaticResource BoolToStatusConverter}}" FontSize="10"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,10,0,0">
                        <Button Name="btnDeleteAccount" Content="🗑️ 删除" Padding="10,5" Click="BtnDeleteAccount_Click" IsEnabled="False"/>
                        <Button Name="btnUseAccount" Content="✅ 使用" Margin="10,0,0,0" Padding="10,5" Click="BtnUseAccount_Click" IsEnabled="False"/>
                        <Button Name="btnValidateAccount" Content="🔍 验证" Margin="10,0,0,0" Padding="10,5" Click="BtnValidateAccount_Click" IsEnabled="False"/>
                    </StackPanel>
                </Grid>
            </GroupBox>
            
            <!-- 右侧：添加/编辑账号 -->
            <GroupBox Grid.Column="1" Header="➕ 添加新账号" Margin="15,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="5">
                        <!-- 平台选择 -->
                        <TextBlock Text="🌐 选择平台:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox Name="cmbPlatform" Height="35" SelectionChanged="CmbPlatform_SelectionChanged">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding Icon}" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <StackPanel>
                                            <TextBlock Text="{Binding Name}" FontWeight="Bold"/>
                                            <TextBlock Text="{Binding Description}" FontSize="10" Foreground="Gray"/>
                                        </StackPanel>
                                    </StackPanel>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <Separator Margin="0,15,0,15"/>

                        <!-- 基本信息 -->
                        <TextBlock Text="👤 账号信息:" FontWeight="Bold" Margin="0,0,0,10"/>

                        <TextBlock Text="用户名/邮箱/手机号:" Margin="0,0,0,5"/>
                        <TextBox Name="txtUsername" Height="30" VerticalContentAlignment="Center"/>

                        <TextBlock Text="昵称 (可选):" Margin="0,10,0,5"/>
                        <TextBox Name="txtNickname" Height="30" VerticalContentAlignment="Center"/>

                        <TextBlock Text="备注 (可选):" Margin="0,10,0,5"/>
                        <TextBox Name="txtNotes" Height="30" VerticalContentAlignment="Center"/>

                        <!-- 登录方式选择 -->
                        <Separator Margin="0,15,0,15"/>
                        <TextBlock Text="🔐 登录方式:" FontWeight="Bold" Margin="0,0,0,10"/>

                        <TabControl Name="tcLoginMethod" Height="280">
                            <!-- 密码登录 -->
                            <TabItem Header="🔑 密码登录">
                                <StackPanel Margin="10">
                                    <TextBlock Text="密码:" Margin="0,0,0,5"/>
                                    <PasswordBox Name="txtPassword" Height="30" VerticalContentAlignment="Center"/>

                                    <CheckBox Name="chkSavePassword" Content="保存密码到本地" Margin="0,15,0,0" IsChecked="True"/>
                                    <TextBlock Text="⚠️ 注意：密码将以明文形式保存在本地文件中"
                                               Foreground="Red" FontSize="10" Margin="0,5,0,0" TextWrapping="Wrap"/>

                                    <Button Name="btnAddAccount" Content="➕ 添加账号" Margin="0,20,0,0" Padding="15,8" Click="BtnAddAccount_Click"/>
                                    <Button Name="btnTestLogin" Content="🔍 测试登录" Margin="0,10,0,0" Padding="15,8" Click="BtnTestLogin_Click"/>
                                </StackPanel>
                            </TabItem>

                            <!-- Cookie登录 -->
                            <TabItem Header="🍪 Cookie登录">
                                <StackPanel Margin="10">
                                    <TextBlock Text="Cookie内容:" Margin="0,0,0,5"/>
                                    <TextBox Name="txtCookie" Height="120" TextWrapping="Wrap" AcceptsReturn="True"
                                             VerticalScrollBarVisibility="Auto" VerticalContentAlignment="Top"
                                             FontFamily="Consolas" FontSize="10"/>

                                    <TextBlock Text="💡 获取Cookie方法：" FontWeight="Bold" Margin="0,10,0,5"/>
                                    <TextBlock TextWrapping="Wrap" FontSize="10" Foreground="#FF666666">
                                        1. 在浏览器中登录对应平台<LineBreak/>
                                        2. 按F12打开开发者工具<LineBreak/>
                                        3. 在Network标签页中刷新页面<LineBreak/>
                                        4. 找到请求头中的Cookie值并复制
                                    </TextBlock>

                                    <Button Name="btnAddCookie" Content="🍪 使用Cookie登录" Margin="0,15,0,0" Padding="15,8" Click="BtnAddCookie_Click"/>
                                </StackPanel>
                            </TabItem>
                        </TabControl>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
        </Grid>
        
        <!-- 底部按钮 -->
        <Border Grid.Row="2" Background="#FFF5F5F5" Padding="15">
            <Grid>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <TextBlock Name="txtAccountStats" Text="📊 统计信息加载中..." VerticalAlignment="Center" Foreground="#FF666666"/>
                </StackPanel>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Name="btnRefreshAccounts" Content="🔄 刷新" Padding="15,8" Click="BtnRefreshAccounts_Click"/>
                    <Button Name="btnOpenAccountFile" Content="📁 打开账号文件" Margin="10,0,0,0" Padding="15,8" Click="BtnOpenAccountFile_Click"/>
                    <Button Name="btnClose" Content="❌ 关闭" Margin="10,0,0,0" Padding="15,8" Click="BtnClose_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
