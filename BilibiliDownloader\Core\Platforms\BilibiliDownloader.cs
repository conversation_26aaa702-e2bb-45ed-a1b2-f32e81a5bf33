using System.Text.RegularExpressions;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class BilibiliPlatformDownloader : IPlatformDownloader
    {
        private readonly BilibiliApi _bilibiliApi;

        public string PlatformName => "哔哩哔哩";
        public string PlatformIcon => "📺";
        public bool RequiresLogin => false; // 部分内容需要登录

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?bilibili\.com/video/[Bb][Vv][\w]+",
            @"https?://(?:www\.)?bilibili\.com/bangumi/play/ep\d+",
            @"https?://(?:www\.)?bilibili\.com/bangumi/play/ss\d+",
            @"https?://b23\.tv/[\w]+",
            @"https?://(?:www\.)?bilibili\.com/s/video/[Bb][Vv][\w]+"
        };

        public BilibiliPlatformDownloader()
        {
            _bilibiliApi = new BilibiliApi();
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析B站视频: {url}");

                // 处理短链接
                var fullUrl = await ResolveShortUrl(url);
                
                // 检查是否为番剧链接
                bool isBangumi = fullUrl.Contains("/bangumi/play/");

                if (isBangumi)
                {
                    Logger.Instance.Info("检测到番剧链接，使用内置解析器...");
                    // 番剧解析可能需要特殊处理，但我们继续使用内置解析器
                }

                // 使用标准模式解析
                bool bypassPayment = false;

                var videoInfo = await _bilibiliApi.GetVideoInfoAsync(fullUrl, bypassPayment);
                
                if (videoInfo != null)
                {
                    videoInfo.Platform = PlatformName;
                    return videoInfo;
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"B站解析失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            try
            {
                var playInfo = await _bilibiliApi.GetVideoPlayInfoAsync(videoInfo.VideoId, 1, false);
                var qualities = new List<QualityOption>();

                if (playInfo?.data?.dash?.video != null)
                {
                    var videoStreams = playInfo.data.dash.video as Newtonsoft.Json.Linq.JArray;
                    if (videoStreams != null)
                    {
                        var qualityMap = new Dictionary<int, string>
                        {
                            { 120, "4K 超清" },
                            { 116, "1080P60 高帧率" },
                            { 112, "1080P+ 高码率" },
                            { 80, "1080P 高清" },
                            { 74, "720P60 高帧率" },
                            { 64, "720P 高清" },
                            { 32, "480P 清晰" },
                            { 16, "360P 流畅" }
                        };

                        foreach (var stream in videoStreams)
                        {
                            var qn = stream["id"]?.ToObject<int>() ?? 0;
                            var width = stream["width"]?.ToObject<int>() ?? 0;
                            var height = stream["height"]?.ToObject<int>() ?? 0;

                            if (qualityMap.ContainsKey(qn))
                            {
                                qualities.Add(new QualityOption
                                {
                                    Quality = qn.ToString(),
                                    Description = qualityMap[qn],
                                    Width = width,
                                    Height = height,
                                    Format = "mp4"
                                });
                            }
                        }
                    }
                }

                // 如果没有获取到清晰度，返回默认选项
                if (qualities.Count == 0)
                {
                    qualities.AddRange(new[]
                    {
                        new QualityOption { Quality = "80", Description = "1080P 高清", Width = 1920, Height = 1080, Format = "mp4" },
                        new QualityOption { Quality = "64", Description = "720P 高清", Width = 1280, Height = 720, Format = "mp4" },
                        new QualityOption { Quality = "32", Description = "480P 清晰", Width = 854, Height = 480, Format = "mp4" },
                        new QualityOption { Quality = "16", Description = "360P 流畅", Width = 640, Height = 360, Format = "mp4" }
                    });
                }

                return qualities;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取B站清晰度失败: {ex.Message}");
                return new List<QualityOption>();
            }
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                var qualityId = int.TryParse(quality, out var qid) ? qid : 80;
                var playInfo = await _bilibiliApi.GetVideoPlayInfoAsync(videoInfo.VideoId, 1, false);
                var downloadUrls = new List<DownloadUrl>();

                if (playInfo?.data?.dash?.video != null && playInfo?.data?.dash?.audio != null)
                {
                    var videoStreams = playInfo.data.dash.video as Newtonsoft.Json.Linq.JArray;
                    var audioStreams = playInfo.data.dash.audio as Newtonsoft.Json.Linq.JArray;

                    // 找到对应清晰度的视频流
                    var videoStream = videoStreams?.FirstOrDefault(v => v["id"]?.Value<int>() == qualityId);
                    var audioStream = audioStreams?.FirstOrDefault();

                    if (videoStream != null)
                    {
                        downloadUrls.Add(new DownloadUrl
                        {
                            Url = videoStream["baseUrl"]?.ToObject<string>() ?? "",
                            Quality = quality,
                            Format = "mp4",
                            FileSize = videoStream["bandwidth"]?.ToObject<long>() ?? 0,
                            Headers = new Dictionary<string, string>
                            {
                                ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                ["Referer"] = "https://www.bilibili.com/"
                            }
                        });
                    }

                    if (audioStream != null)
                    {
                        downloadUrls.Add(new DownloadUrl
                        {
                            Url = audioStream["baseUrl"]?.ToObject<string>() ?? "",
                            Quality = "Audio",
                            Format = "mp3",
                            FileSize = audioStream["bandwidth"]?.ToObject<long>() ?? 0,
                            Headers = new Dictionary<string, string>
                            {
                                ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                ["Referer"] = "https://www.bilibili.com/"
                            }
                        });
                    }
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取B站下载链接失败: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                if (string.IsNullOrEmpty(cookie)) return true; // B站可以不登录

                // 简单验证：尝试获取用户信息
                return !string.IsNullOrEmpty(cookie);
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> ResolveShortUrl(string url)
        {
            try
            {
                if (url.Contains("b23.tv"))
                {
                    using var httpClient = new HttpClient();
                    var request = new HttpRequestMessage(HttpMethod.Head, url);
                    var response = await httpClient.SendAsync(request);
                    
                    if (response.Headers.Location != null)
                    {
                        return response.Headers.Location.ToString();
                    }
                }
                return url;
            }
            catch
            {
                return url;
            }
        }

        public void Dispose()
        {
            // BilibiliApi 的清理工作
        }
    }
}
