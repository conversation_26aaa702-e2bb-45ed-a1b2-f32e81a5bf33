using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class XiaohongshuDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "小红书";
        public string PlatformIcon => "📖";
        public bool RequiresLogin => true; // 小红书通常需要登录

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?xiaohongshu\.com/explore/[\w]+",
            @"https?://(?:www\.)?xiaohongshu\.com/discovery/item/[\w]+",
            @"https?://xhslink\.com/[\w]+",
            @"http://xhslink\.com/[\w]+"
        };

        public XiaohongshuDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析小红书内容: {url}");

                if (string.IsNullOrEmpty(cookie))
                {
                    throw new Exception("小红书需要登录才能解析内容，请在账号管理中添加小红书账号");
                }

                // 处理短链接
                var fullUrl = await ResolveShortUrl(url);
                var noteId = ExtractNoteId(fullUrl);

                if (string.IsNullOrEmpty(noteId))
                {
                    throw new Exception("无法提取笔记ID");
                }

                var noteInfo = await GetNoteInfoFromApi(noteId, cookie);
                
                return new VideoInfo
                {
                    Title = noteInfo.title ?? "小红书笔记",
                    Author = noteInfo.user?.nickname ?? "未知作者",
                    Duration = noteInfo.video?.duration ?? 0,
                    Description = noteInfo.desc ?? "",
                    CoverUrl = noteInfo.cover?.url ?? "",
                    VideoId = noteId,
                    Url = fullUrl,
                    Platform = PlatformName,
                    PublishTime = DateTime.TryParse(noteInfo.time, out DateTime date) ? date : DateTime.Now,
                    ViewCount = noteInfo.view_count ?? 0,
                    IsCollection = false,
                    IsPaymentRequired = false
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"小红书解析失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "1080p", Description = "高清", Width = 1080, Height = 1920, Format = "mp4" },
                new() { Quality = "720p", Description = "标清", Width = 720, Height = 1280, Format = "mp4" },
                new() { Quality = "540p", Description = "流畅", Width = 540, Height = 960, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                if (string.IsNullOrEmpty(cookie))
                {
                    throw new Exception("需要登录小红书账号");
                }

                var downloadUrls = new List<DownloadUrl>();

                // 视频下载链接
                downloadUrls.Add(new DownloadUrl
                {
                    Url = $"https://example.com/xiaohongshu/{videoInfo.VideoId}/{quality}.mp4",
                    Quality = quality,
                    Format = "mp4",
                    FileSize = EstimateFileSize(quality),
                    Headers = new Dictionary<string, string>
                    {
                        ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                        ["Referer"] = "https://www.xiaohongshu.com/",
                        ["Cookie"] = cookie
                    }
                });

                // 图片下载链接（如果是图文笔记）
                for (int i = 1; i <= 9; i++) // 小红书最多9张图
                {
                    downloadUrls.Add(new DownloadUrl
                    {
                        Url = $"https://example.com/xiaohongshu/{videoInfo.VideoId}/image_{i}.jpg",
                        Quality = "原图",
                        Format = "jpg",
                        FileSize = 2 * 1024 * 1024, // 2MB
                        Headers = new Dictionary<string, string>
                        {
                            ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                            ["Referer"] = "https://www.xiaohongshu.com/",
                            ["Cookie"] = cookie
                        }
                    });
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取小红书下载链接失败: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                if (string.IsNullOrEmpty(cookie)) return false;

                var request = new HttpRequestMessage(HttpMethod.Get, "https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo");
                request.Headers.Add("Cookie", cookie);

                var response = await _httpClient.SendAsync(request);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> ResolveShortUrl(string url)
        {
            try
            {
                if (url.Contains("xhslink.com"))
                {
                    var request = new HttpRequestMessage(HttpMethod.Head, url);
                    var response = await _httpClient.SendAsync(request);
                    
                    if (response.Headers.Location != null)
                    {
                        return response.Headers.Location.ToString();
                    }
                }
                return url;
            }
            catch
            {
                return url;
            }
        }

        private string ExtractNoteId(string url)
        {
            var patterns = new[]
            {
                @"xiaohongshu\.com/explore/([\w]+)",
                @"xiaohongshu\.com/discovery/item/([\w]+)",
                @"explore/([\w]+)",
                @"item/([\w]+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        private async Task<dynamic> GetNoteInfoFromApi(string noteId, string cookie)
        {
            // 模拟API调用
            await Task.Delay(100);

            return new
            {
                title = "小红书精彩内容",
                user = new { nickname = "小红书用户" },
                desc = "分享生活的美好瞬间",
                cover = new { url = $"https://example.com/xiaohongshu/{noteId}/cover.jpg" },
                video = new { duration = 30 },
                time = DateTime.Now.ToString("yyyy-MM-dd"),
                view_count = 10000
            };
        }

        private long EstimateFileSize(string quality)
        {
            return quality switch
            {
                "1080p" => 50 * 1024 * 1024L,  // 50MB
                "720p" => 30 * 1024 * 1024L,   // 30MB
                "540p" => 20 * 1024 * 1024L,   // 20MB
                _ => 30 * 1024 * 1024L
            };
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
