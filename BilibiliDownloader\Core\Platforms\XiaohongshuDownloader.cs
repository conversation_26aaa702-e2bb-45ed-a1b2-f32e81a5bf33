using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class XiaohongshuDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "小红书";
        public string PlatformIcon => "📖";
        public bool RequiresLogin => true; // 小红书通常需要登录

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?xiaohongshu\.com/explore/[\w]+",
            @"https?://(?:www\.)?xiaohongshu\.com/discovery/item/[\w]+",
            @"https?://xhslink\.com/[\w]+",
            @"http://xhslink\.com/[\w]+"
        };

        public XiaohongshuDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析小红书内容: {url}");

                if (string.IsNullOrEmpty(cookie))
                {
                    throw new Exception("小红书需要登录才能解析内容，请在账号管理中添加小红书账号");
                }

                // 设置Cookie
                _httpClient.DefaultRequestHeaders.Remove("Cookie");
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);

                // 处理短链接
                var fullUrl = await ResolveShortUrl(url);
                var noteId = ExtractNoteId(fullUrl);

                if (string.IsNullOrEmpty(noteId))
                {
                    throw new Exception("无法提取笔记ID");
                }

                // 获取笔记信息
                var videoInfo = await GetXiaohongshuNoteInfoAsync(noteId, fullUrl);
                if (videoInfo != null)
                {
                    videoInfo.Platform = PlatformName;
                    videoInfo.OriginalUrl = url;
                }

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"小红书解析失败: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> GetXiaohongshuNoteInfoAsync(string noteId, string fullUrl)
        {
            try
            {
                // 获取网页内容
                var pageContent = await _httpClient.GetStringAsync(fullUrl);

                // 从页面中提取笔记信息
                var title = ExtractTitleFromPage(pageContent) ?? $"小红书笔记 {noteId}";
                var author = ExtractAuthorFromPage(pageContent) ?? "未知作者";
                var description = ExtractDescriptionFromPage(pageContent) ?? "";
                var coverUrl = ExtractCoverFromPage(pageContent) ?? "";
                var isVideo = CheckIfVideo(pageContent);
                var duration = isVideo ? ExtractDurationFromPage(pageContent) : 0;

                return new VideoInfo
                {
                    VideoId = noteId,
                    Title = title,
                    Author = author,
                    Description = description,
                    CoverUrl = coverUrl,
                    Duration = duration,
                    ViewCount = 0, // 小红书不公开具体数据
                    PublishTime = DateTime.Now, // 小红书API限制
                    IsCollection = false,
                    IsPaymentRequired = false,
                    Pages = new List<VideoPage>
                    {
                        new VideoPage
                        {
                            Page = 1,
                            Cid = 0,
                            Part = title,
                            Duration = duration
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取小红书笔记信息失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "1080p", Description = "高清", Width = 1080, Height = 1920, Format = "mp4" },
                new() { Quality = "720p", Description = "标清", Width = 720, Height = 1280, Format = "mp4" },
                new() { Quality = "540p", Description = "流畅", Width = 540, Height = 960, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                if (string.IsNullOrEmpty(cookie))
                {
                    throw new Exception("需要登录小红书账号");
                }

                Logger.Instance.Info($"获取小红书下载链接: {videoInfo.VideoId}, 质量: {quality}");

                // 设置Cookie
                _httpClient.DefaultRequestHeaders.Remove("Cookie");
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);

                // 尝试获取实际的下载链接
                var downloadUrls = await GetXiaohongshuDownloadUrlsAsync(videoInfo.VideoId, quality, cookie);

                if (downloadUrls.Count == 0)
                {
                    Logger.Instance.Warning("无法获取小红书直接下载链接");
                    throw new Exception("小红书下载需要特殊处理。由于小红书的反爬虫机制，无法直接获取下载链接。");
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取小红书下载链接失败: {ex.Message}");
                throw new Exception($"小红书下载失败: {ex.Message}\n\n建议：\n1. 确保已登录小红书账号\n2. 检查网络连接\n3. 确认内容是否可用");
            }
        }

        private async Task<List<DownloadUrl>> GetXiaohongshuDownloadUrlsAsync(string noteId, string quality, string cookie)
        {
            try
            {
                var downloadUrls = new List<DownloadUrl>();

                // 构建小红书笔记页面URL
                var noteUrl = $"https://www.xiaohongshu.com/explore/{noteId}";

                // 获取页面内容
                var pageContent = await _httpClient.GetStringAsync(noteUrl);

                // 检查是视频还是图文
                var isVideo = CheckIfVideo(pageContent);

                if (isVideo)
                {
                    // 提取视频下载链接
                    var videoUrls = ExtractVideoUrlsFromPage(pageContent, quality);

                    foreach (var url in videoUrls)
                    {
                        if (!string.IsNullOrEmpty(url))
                        {
                            downloadUrls.Add(new DownloadUrl
                            {
                                Url = url,
                                Quality = quality,
                                Format = "mp4",
                                FileSize = EstimateFileSize(quality),
                                Headers = new Dictionary<string, string>
                                {
                                    ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                    ["Referer"] = "https://www.xiaohongshu.com/",
                                    ["Cookie"] = cookie
                                }
                            });
                        }
                    }
                }
                else
                {
                    // 提取图片下载链接
                    var imageUrls = ExtractImageUrlsFromPage(pageContent);

                    foreach (var url in imageUrls)
                    {
                        if (!string.IsNullOrEmpty(url))
                        {
                            downloadUrls.Add(new DownloadUrl
                            {
                                Url = url,
                                Quality = "原图",
                                Format = "jpg",
                                FileSize = 2 * 1024 * 1024, // 2MB
                                Headers = new Dictionary<string, string>
                                {
                                    ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                    ["Referer"] = "https://www.xiaohongshu.com/",
                                    ["Cookie"] = cookie
                                }
                            });
                        }
                    }
                }

                // 如果没有找到链接，尝试使用API方式
                if (downloadUrls.Count == 0)
                {
                    var apiUrls = await GetXiaohongshuUrlsFromApi(noteId, quality, cookie);
                    downloadUrls.AddRange(apiUrls);
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"小红书下载URL获取异常: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        private List<string> ExtractVideoUrlsFromPage(string pageContent, string quality)
        {
            var urls = new List<string>();

            try
            {
                // 尝试提取视频链接
                var patterns = new[]
                {
                    @"""video_url"":""([^""]+)""",
                    @"""stream"":{[^}]*""h264"":\[{[^}]*""master_url"":""([^""]+)""",
                    @"""url_list"":\[""([^""]+)""",
                    @"""play_info"":{[^}]*""url"":""([^""]+)"""
                };

                foreach (var pattern in patterns)
                {
                    var matches = Regex.Matches(pageContent, pattern);
                    foreach (Match match in matches)
                    {
                        var url = match.Groups[1].Value.Replace("\\u002F", "/").Replace("\\/", "/");
                        if (!string.IsNullOrEmpty(url) && url.StartsWith("http"))
                        {
                            urls.Add(url);
                        }
                    }
                }

                // 去重
                return urls.Distinct().ToList();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取小红书视频链接失败: {ex.Message}");
                return urls;
            }
        }

        private List<string> ExtractImageUrlsFromPage(string pageContent)
        {
            var urls = new List<string>();

            try
            {
                // 尝试提取图片链接
                var patterns = new[]
                {
                    @"""url"":""([^""]+\.jpg[^""]*)""",
                    @"""url"":""([^""]+\.jpeg[^""]*)""",
                    @"""url"":""([^""]+\.png[^""]*)""",
                    @"""url"":""([^""]+\.webp[^""]*)""",
                    @"""trace_id"":""[^""]*"",""url"":""([^""]+)"""
                };

                foreach (var pattern in patterns)
                {
                    var matches = Regex.Matches(pageContent, pattern);
                    foreach (Match match in matches)
                    {
                        var url = match.Groups[1].Value.Replace("\\u002F", "/").Replace("\\/", "/");
                        if (!string.IsNullOrEmpty(url) && url.StartsWith("http") &&
                            (url.Contains(".jpg") || url.Contains(".jpeg") || url.Contains(".png") || url.Contains(".webp")))
                        {
                            urls.Add(url);
                        }
                    }
                }

                // 去重
                return urls.Distinct().ToList();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"提取小红书图片链接失败: {ex.Message}");
                return urls;
            }
        }

        private async Task<List<DownloadUrl>> GetXiaohongshuUrlsFromApi(string noteId, string quality, string cookie)
        {
            try
            {
                // 尝试使用小红书的API
                var apiUrl = $"https://www.xiaohongshu.com/api/sns/web/v1/feed?source_note_id={noteId}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Add("Cookie", cookie);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var jsonDoc = JsonDocument.Parse(content);

                    if (jsonDoc.RootElement.TryGetProperty("data", out var data) &&
                        data.TryGetProperty("items", out var items) &&
                        items.GetArrayLength() > 0)
                    {
                        var item = items[0];
                        var downloadUrls = new List<DownloadUrl>();

                        // 检查是否有视频
                        if (item.TryGetProperty("note_card", out var noteCard) &&
                            noteCard.TryGetProperty("video", out var video) &&
                            video.TryGetProperty("media", out var media) &&
                            media.TryGetProperty("stream", out var stream))
                        {
                            // 视频内容
                            if (stream.TryGetProperty("h264", out var h264) &&
                                h264.GetArrayLength() > 0)
                            {
                                var videoStream = h264[0];
                                if (videoStream.TryGetProperty("master_url", out var masterUrl))
                                {
                                    var videoUrl = masterUrl.GetString();
                                    if (!string.IsNullOrEmpty(videoUrl))
                                    {
                                        downloadUrls.Add(new DownloadUrl
                                        {
                                            Url = videoUrl,
                                            Quality = quality,
                                            Format = "mp4",
                                            FileSize = EstimateFileSize(quality),
                                            Headers = new Dictionary<string, string>
                                            {
                                                ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                                ["Referer"] = "https://www.xiaohongshu.com/",
                                                ["Cookie"] = cookie
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        else if (noteCard.TryGetProperty("image_list", out var imageList))
                        {
                            // 图片内容
                            foreach (var image in imageList.EnumerateArray())
                            {
                                if (image.TryGetProperty("url", out var imageUrl))
                                {
                                    var url = imageUrl.GetString();
                                    if (!string.IsNullOrEmpty(url))
                                    {
                                        downloadUrls.Add(new DownloadUrl
                                        {
                                            Url = url,
                                            Quality = "原图",
                                            Format = "jpg",
                                            FileSize = 2 * 1024 * 1024,
                                            Headers = new Dictionary<string, string>
                                            {
                                                ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                                                ["Referer"] = "https://www.xiaohongshu.com/",
                                                ["Cookie"] = cookie
                                            }
                                        });
                                    }
                                }
                            }
                        }

                        return downloadUrls;
                    }
                }

                return new List<DownloadUrl>();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"小红书API获取失败: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                if (string.IsNullOrEmpty(cookie)) return false;

                var request = new HttpRequestMessage(HttpMethod.Get, "https://www.xiaohongshu.com/api/sns/web/v1/user/selfinfo");
                request.Headers.Add("Cookie", cookie);

                var response = await _httpClient.SendAsync(request);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> ResolveShortUrl(string url)
        {
            try
            {
                if (url.Contains("xhslink.com"))
                {
                    var request = new HttpRequestMessage(HttpMethod.Head, url);
                    var response = await _httpClient.SendAsync(request);
                    
                    if (response.Headers.Location != null)
                    {
                        return response.Headers.Location.ToString();
                    }
                }
                return url;
            }
            catch
            {
                return url;
            }
        }

        private string? ExtractTitleFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取标题
                var titleMatch = Regex.Match(pageContent, @"""title"":""([^""]+)""");
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Replace("\\n", " ").Replace("\\\"", "\"");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractAuthorFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取作者信息
                var authorMatch = Regex.Match(pageContent, @"""nickname"":""([^""]+)""");
                if (authorMatch.Success)
                {
                    return authorMatch.Groups[1].Value;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractDescriptionFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取描述
                var descMatch = Regex.Match(pageContent, @"""desc"":""([^""]+)""");
                if (descMatch.Success)
                {
                    return descMatch.Groups[1].Value.Replace("\\n", "\n").Replace("\\\"", "\"");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractCoverFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取封面图
                var coverMatch = Regex.Match(pageContent, @"""cover"":""([^""]+)""");
                if (coverMatch.Success)
                {
                    return coverMatch.Groups[1].Value.Replace("\\u002F", "/");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private bool CheckIfVideo(string pageContent)
        {
            try
            {
                // 检查是否为视频内容
                return pageContent.Contains("\"type\":\"video\"") || pageContent.Contains("video_url");
            }
            catch
            {
                return false;
            }
        }

        private long ExtractDurationFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取视频时长
                var durationMatch = Regex.Match(pageContent, @"""duration"":(\d+)");
                if (durationMatch.Success && long.TryParse(durationMatch.Groups[1].Value, out var duration))
                {
                    return duration;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private string ExtractNoteId(string url)
        {
            var patterns = new[]
            {
                @"xiaohongshu\.com/explore/([\w]+)",
                @"xiaohongshu\.com/discovery/item/([\w]+)",
                @"explore/([\w]+)",
                @"item/([\w]+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        private async Task<dynamic> GetNoteInfoFromApi(string noteId, string cookie)
        {
            // 模拟API调用
            await Task.Delay(100);

            return new
            {
                title = "小红书精彩内容",
                user = new { nickname = "小红书用户" },
                desc = "分享生活的美好瞬间",
                cover = new { url = $"https://example.com/xiaohongshu/{noteId}/cover.jpg" },
                video = new { duration = 30 },
                time = DateTime.Now.ToString("yyyy-MM-dd"),
                view_count = 10000
            };
        }

        private long EstimateFileSize(string quality)
        {
            return quality switch
            {
                "1080p" => 50 * 1024 * 1024L,  // 50MB
                "720p" => 30 * 1024 * 1024L,   // 30MB
                "540p" => 20 * 1024 * 1024L,   // 20MB
                _ => 30 * 1024 * 1024L
            };
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
