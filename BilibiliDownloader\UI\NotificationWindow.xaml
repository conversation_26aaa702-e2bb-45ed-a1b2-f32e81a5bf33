<Window x:Class="BilibiliDownloader.UI.NotificationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="通知" Height="120" Width="350"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Topmost="True"
        ShowInTaskbar="False"
        ResizeMode="NoResize">
    
    <Border Background="#FF2B2B2B" CornerRadius="8" BorderBrush="#FF555555" BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" Opacity="0.3"/>
        </Border.Effect>
        
        <Grid Margin="15">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Name="txtTitle" Grid.Column="0" Text="下载完成" 
                          Foreground="White" FontWeight="Bold" FontSize="14"/>
                
                <Button Name="btnClose" Grid.Column="1" Content="×" 
                       Width="20" Height="20" 
                       Background="Transparent" 
                       Foreground="White" 
                       BorderThickness="0"
                       FontSize="16"
                       Click="BtnClose_Click"/>
            </Grid>
            
            <!-- 内容 -->
            <TextBlock Name="txtMessage" Grid.Row="1" 
                      Text="下载任务已完成" 
                      Foreground="#FFCCCCCC" 
                      TextWrapping="Wrap"
                      Margin="0,8,0,0"/>
            
            <!-- 进度条（可选） -->
            <ProgressBar Name="progressBar" Grid.Row="2" 
                        Height="4" 
                        Margin="0,8,0,0"
                        Background="#FF444444"
                        Foreground="#FF00AA00"
                        Visibility="Collapsed"/>
        </Grid>
    </Border>
</Window>
