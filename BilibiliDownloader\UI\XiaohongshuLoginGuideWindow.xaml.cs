using System.Diagnostics;
using System.Windows;

namespace BilibiliDownloader.UI
{
    public partial class XiaohongshuLoginGuideWindow : Window
    {
        public XiaohongshuLoginGuideWindow()
        {
            InitializeComponent();
        }

        private void OpenXiaohongshuButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 打开小红书官网
                Process.Start(new ProcessStartInfo
                {
                    FileName = "https://www.xiaohongshu.com",
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开网页: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenAccountManagerButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 打开账号管理窗口
                var accountManager = new BilibiliDownloader.Utils.AccountManager();
                var accountManagerWindow = new AccountManagerWindow(accountManager);
                accountManagerWindow.Owner = this.Owner; // 设置父窗口
                accountManagerWindow.Show();

                // 关闭当前窗口
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开账号管理: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
