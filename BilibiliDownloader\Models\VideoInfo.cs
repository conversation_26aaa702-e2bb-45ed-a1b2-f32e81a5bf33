using System;
using System.Collections.Generic;

namespace BilibiliDownloader.Models
{
    public class VideoInfo
    {
        public string VideoId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Author { get; set; }
        public string AuthorId { get; set; }
        public DateTime PublishTime { get; set; }
        public long Duration { get; set; }
        public string CoverUrl { get; set; }
        public List<VideoQuality> AvailableQualities { get; set; }
        public bool IsPaymentRequired { get; set; }
        public bool IsVip { get; set; }
        public bool IsCollection { get; set; }
        public List<VideoInfo> CollectionVideos { get; set; }
        public string OriginalUrl { get; set; }
        
        public VideoInfo()
        {
            AvailableQualities = new List<VideoQuality>();
            CollectionVideos = new List<VideoInfo>();
        }
    }
    
    public class VideoQuality
    {
        public int Quality { get; set; }
        public string QualityName { get; set; }
        public string VideoUrl { get; set; }
        public string AudioUrl { get; set; }
        public long FileSize { get; set; }
        public string Format { get; set; }
        public string Codec { get; set; }
        
        public override string ToString()
        {
            return QualityName;
        }
    }
}
