using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace BilibiliDownloader.Models
{
    public class VideoInfo
    {
        public string VideoId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Author { get; set; }
        public string AuthorId { get; set; }
        public DateTime PublishTime { get; set; }
        public long Duration { get; set; }
        public string CoverUrl { get; set; }
        public List<VideoQuality> AvailableQualities { get; set; }
        public bool IsPaymentRequired { get; set; }
        public bool IsVip { get; set; }
        public bool IsCollection { get; set; }
        public List<VideoInfo> CollectionVideos { get; set; }
        public string OriginalUrl { get; set; }
        public int CurrentPage { get; set; } = 1;
        public List<VideoPage> Pages { get; set; }
        
        public VideoInfo()
        {
            AvailableQualities = new List<VideoQuality>();
            CollectionVideos = new List<VideoInfo>();
            Pages = new List<VideoPage>();
        }

        /// <summary>
        /// 生成安全的文件夹名称
        /// </summary>
        public string GetSafeFolderName()
        {
            var folderName = Title ?? VideoId ?? "Unknown";

            // 移除或替换不安全的字符
            var invalidChars = Path.GetInvalidFileNameChars();
            foreach (var c in invalidChars)
            {
                folderName = folderName.Replace(c, '_');
            }

            // 限制长度，避免路径过长
            if (folderName.Length > 50)
            {
                folderName = folderName.Substring(0, 50);
            }

            // 添加VideoId确保唯一性
            if (!string.IsNullOrEmpty(VideoId))
            {
                folderName = $"{folderName}_{VideoId}";
            }

            return folderName.Trim();
        }
    }
    
    public class VideoQuality
    {
        public int Quality { get; set; }
        public string QualityName { get; set; }
        public string VideoUrl { get; set; }
        public string AudioUrl { get; set; }
        public long FileSize { get; set; }
        public string Format { get; set; }
        public string Codec { get; set; }
        
        public override string ToString()
        {
            return QualityName;
        }
    }

    public class VideoPage
    {
        public int Page { get; set; }
        public long Cid { get; set; }
        public string Part { get; set; }
        public long Duration { get; set; }
        public string Dimension { get; set; }

        public override string ToString()
        {
            return $"P{Page}: {Part}";
        }
    }
}
