using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace BilibiliDownloader.Models
{
    public class VideoInfo
    {
        public string VideoId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Author { get; set; }
        public string AuthorId { get; set; }
        public DateTime PublishTime { get; set; }
        public long Duration { get; set; }
        public string CoverUrl { get; set; }
        public List<VideoQuality> AvailableQualities { get; set; }
        public bool IsPaymentRequired { get; set; }
        public bool IsVip { get; set; }
        public bool IsCollection { get; set; }
        public List<VideoInfo> CollectionVideos { get; set; }
        public string OriginalUrl { get; set; }
        public string Url { get; set; } // 新增：视频URL
        public string Platform { get; set; } // 新增：平台名称
        public long ViewCount { get; set; } // 新增：观看次数
        public int CurrentPage { get; set; } = 1;
        public List<VideoPage> Pages { get; set; }
        public string CollectionTitle { get; set; } // 合集标题，用于文件夹命名
        
        public VideoInfo()
        {
            AvailableQualities = new List<VideoQuality>();
            CollectionVideos = new List<VideoInfo>();
            Pages = new List<VideoPage>();
        }

        /// <summary>
        /// 生成安全的文件夹名称
        /// </summary>
        public string GetSafeFolderName()
        {
            // 优先使用合集标题，如果没有则使用视频标题
            var folderName = !string.IsNullOrEmpty(CollectionTitle) ? CollectionTitle : (Title ?? VideoId ?? "Unknown");

            // 移除或替换不安全的字符
            var invalidChars = Path.GetInvalidFileNameChars();
            foreach (var c in invalidChars)
            {
                folderName = folderName.Replace(c, '_');
            }

            // 替换一些常见的特殊字符，使文件夹名更清晰
            folderName = folderName.Replace(":", "：")  // 中文冒号
                                   .Replace("?", "？")  // 中文问号
                                   .Replace("*", "★")  // 星号
                                   .Replace("\"", "'") // 引号
                                   .Replace("|", "｜"); // 竖线

            // 限制长度，避免路径过长（增加到100个字符）
            if (folderName.Length > 100)
            {
                folderName = folderName.Substring(0, 100);
            }

            // 添加VideoId确保唯一性（只有在不是合集时才添加）
            if (string.IsNullOrEmpty(CollectionTitle) && !string.IsNullOrEmpty(VideoId))
            {
                folderName = $"{folderName}_{VideoId}";
            }

            return folderName.Trim();
        }
    }
    
    public class VideoQuality
    {
        public int Quality { get; set; }
        public string QualityName { get; set; }
        public string VideoUrl { get; set; }
        public string AudioUrl { get; set; }
        public long FileSize { get; set; }
        public string Format { get; set; }
        public string Codec { get; set; }
        
        public override string ToString()
        {
            return QualityName;
        }
    }

    public class VideoPage
    {
        public int Page { get; set; }
        public long Cid { get; set; }
        public string Part { get; set; }
        public long Duration { get; set; }
        public string Dimension { get; set; }
        public long EpId { get; set; } // 番剧集数ID

        public override string ToString()
        {
            return $"P{Page}: {Part}";
        }
    }

    public class VideoUrl
    {
        public int Quality { get; set; }
        public string QualityName { get; set; }
        public string Url { get; set; }
        public long Size { get; set; }
        public string Type { get; set; } // "video", "audio"
        public string Format { get; set; }
        public string Codec { get; set; }

        public override string ToString()
        {
            return $"{QualityName} ({Type})";
        }
    }
}
