using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core
{
    public class BilibiliApi
    {
        private readonly HttpClient _httpClient;
        private string _currentCookie;
        
        public BilibiliApi()
        {
            _httpClient = new HttpClient();
            SetupHttpClient();
        }
        
        private void SetupHttpClient()
        {
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Referer", "https://www.bilibili.com/");
        }
        
        public void SetCookie(string cookie)
        {
            _currentCookie = cookie;
            _httpClient.DefaultRequestHeaders.Remove("Cookie");
            if (!string.IsNullOrEmpty(cookie))
            {
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
            }
        }
        
        public async Task<VideoInfo> GetVideoInfoAsync(string url)
        {
            try
            {
                Logger.Instance.Info($"开始解析视频链接: {url}");

                var videoId = ExtractVideoId(url);
                if (string.IsNullOrEmpty(videoId))
                {
                    Logger.Instance.Error($"无效的视频链接: {url}");
                    throw new ArgumentException("无效的视频链接");
                }

                Logger.Instance.Info($"提取到视频ID: {videoId}");

                // 获取视频基本信息
                var videoInfo = await GetVideoBasicInfoAsync(videoId);
                Logger.Instance.Info($"获取视频基本信息成功: {videoInfo.Title}");

                // 获取视频播放信息
                var playInfo = await GetVideoPlayInfoAsync(videoId);
                Logger.Instance.Info("获取视频播放信息成功");

                // 合并信息
                MergeVideoInfo(videoInfo, playInfo);
                Logger.Instance.Info($"视频信息解析完成，可用清晰度: {videoInfo.AvailableQualities.Count}");

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取视频信息失败: {ex.Message}", ex);
                throw new Exception($"获取视频信息失败: {ex.Message}", ex);
            }
        }
        
        private string ExtractVideoId(string url)
        {
            try
            {
                // 支持多种bilibili链接格式
                var patterns = new[]
                {
                    @"bilibili\.com/video/(BV[A-Za-z0-9]+)",  // BV号
                    @"bilibili\.com/video/av(\d+)",           // AV号
                    @"b23\.tv/([A-Za-z0-9]+)",               // 短链接
                    @"bilibili\.com/video/([A-Za-z0-9]+)",   // 通用格式
                    @"(?:bv|BV)([A-Za-z0-9]+)",              // 直接BV号
                    @"(?:av|AV)(\d+)"                        // 直接AV号
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(url, pattern, RegexOptions.IgnoreCase);
                    if (match.Success)
                    {
                        var videoId = match.Groups[1].Value;

                        // 如果是AV号，需要转换为BV号或直接使用
                        if (Regex.IsMatch(videoId, @"^\d+$"))
                        {
                            // 这是AV号，可以直接使用或转换为BV号
                            // 这里暂时直接返回，后续可以添加AV转BV的逻辑
                            return "av" + videoId;
                        }

                        // 确保BV号格式正确
                        if (!videoId.StartsWith("BV", StringComparison.OrdinalIgnoreCase))
                        {
                            videoId = "BV" + videoId;
                        }

                        return videoId;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"解析视频ID失败: {ex.Message}", ex);
            }
        }
        
        private async Task<VideoInfo> GetVideoBasicInfoAsync(string videoId)
        {
            var apiUrl = $"https://api.bilibili.com/x/web-interface/view?bvid={videoId}";

            try
            {
                var response = await _httpClient.GetStringAsync(apiUrl);
                var jsonData = JsonConvert.DeserializeObject<dynamic>(response);

                if (jsonData.code != 0)
                {
                    throw new Exception($"API错误 (code: {jsonData.code}): {jsonData.message}");
                }

                var data = jsonData.data;
                var videoInfo = new VideoInfo
                {
                    VideoId = videoId,
                    Title = data.title?.ToString() ?? "未知标题",
                    Description = data.desc?.ToString() ?? "",
                    Author = data.owner?.name?.ToString() ?? "未知作者",
                    AuthorId = data.owner?.mid?.ToString() ?? "",
                    Duration = Convert.ToInt64(data.duration ?? 0),
                    CoverUrl = data.pic?.ToString() ?? "",
                    OriginalUrl = $"https://www.bilibili.com/video/{videoId}",
                    PublishTime = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(data.pubdate ?? 0)).DateTime
                };

                // 检查是否需要付费
                if (data.rights != null)
                {
                    videoInfo.IsPaymentRequired = Convert.ToBoolean(data.rights.pay ?? 0);
                    videoInfo.IsVip = Convert.ToBoolean(data.rights.hd5 ?? 0);
                }

                // 检查是否是合集
                if (data.pages != null)
                {
                    var pagesArray = data.pages as Newtonsoft.Json.Linq.JArray;
                    if (pagesArray != null && pagesArray.Count > 1)
                    {
                        videoInfo.IsCollection = true;
                    }
                }

                return videoInfo;
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"网络请求失败: {ex.Message}", ex);
            }
            catch (JsonException ex)
            {
                throw new Exception($"JSON解析失败: {ex.Message}", ex);
            }
        }
        
        private async Task<dynamic> GetVideoPlayInfoAsync(string videoId)
        {
            try
            {
                // 首先获取cid
                var cidUrl = $"https://api.bilibili.com/x/player/pagelist?bvid={videoId}";
                var cidResponse = await _httpClient.GetStringAsync(cidUrl);
                var cidData = JsonConvert.DeserializeObject<dynamic>(cidResponse);

                if (cidData.code != 0)
                {
                    throw new Exception($"获取CID失败 (code: {cidData.code}): {cidData.message}");
                }

                var cid = cidData.data[0].cid;

                // 获取播放信息 - 使用更新的参数
                var playUrl = $"https://api.bilibili.com/x/player/playurl?bvid={videoId}&cid={cid}&qn=120&fnval=4048&fourk=1&try_look=1";

                // 添加更多请求头
                using var request = new HttpRequestMessage(HttpMethod.Get, playUrl);
                request.Headers.Add("Accept", "application/json, text/plain, */*");
                request.Headers.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                request.Headers.Add("Origin", "https://www.bilibili.com");
                request.Headers.Add("Sec-Fetch-Dest", "empty");
                request.Headers.Add("Sec-Fetch-Mode", "cors");
                request.Headers.Add("Sec-Fetch-Site", "same-site");

                var playResponse = await _httpClient.SendAsync(request);
                var responseContent = await playResponse.Content.ReadAsStringAsync();
                var playData = JsonConvert.DeserializeObject<dynamic>(responseContent);

                return playData;
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"获取播放信息失败: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"解析播放信息失败: {ex.Message}", ex);
            }
        }
        
        private void MergeVideoInfo(VideoInfo videoInfo, dynamic playInfo)
        {
            if (playInfo.code != 0)
            {
                // 如果获取播放信息失败，可能是权限问题
                if (playInfo.code == -10403)
                {
                    videoInfo.IsPaymentRequired = true;
                }
                return;
            }
            
            var data = playInfo.data;
            
            // 解析可用的清晰度
            if (data.dash != null)
            {
                // DASH格式（音视频分离）
                ParseDashQualities(videoInfo, data.dash);
            }
            else if (data.durl != null)
            {
                // 传统格式（音视频合并）
                ParseTraditionalQualities(videoInfo, data.durl);
            }
        }
        
        private void ParseDashQualities(VideoInfo videoInfo, dynamic dash)
        {
            if (dash.video != null)
            {
                foreach (var video in dash.video)
                {
                    var qualityId = Convert.ToInt32(video.id);
                    var quality = new VideoQuality
                    {
                        Quality = qualityId,
                        QualityName = GetQualityName(qualityId),
                        VideoUrl = video.baseUrl?.ToString(),
                        FileSize = Convert.ToInt64(video.bandwidth ?? 0),
                        Format = "mp4",
                        Codec = video.codecs?.ToString()
                    };
                    
                    // 查找对应的音频流
                    if (dash.audio != null && dash.audio.Count > 0)
                    {
                        quality.AudioUrl = dash.audio[0].baseUrl;
                    }
                    
                    videoInfo.AvailableQualities.Add(quality);
                }
            }
        }
        
        private void ParseTraditionalQualities(VideoInfo videoInfo, dynamic durl)
        {
            foreach (var item in durl)
            {
                var qualityId = Convert.ToInt32(item.quality ?? 80);
                var quality = new VideoQuality
                {
                    Quality = qualityId,
                    QualityName = GetQualityName(qualityId),
                    VideoUrl = item.url?.ToString(),
                    FileSize = Convert.ToInt64(item.size ?? 0),
                    Format = "flv"
                };
                
                videoInfo.AvailableQualities.Add(quality);
            }
        }
        
        private string GetQualityName(int quality)
        {
            return quality switch
            {
                120 => "4K",
                116 => "1080P60",
                112 => "1080P+",
                80 => "1080P",
                74 => "720P60",
                64 => "720P",
                32 => "480P",
                16 => "360P",
                _ => $"质量{quality}"
            };
        }
        
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
