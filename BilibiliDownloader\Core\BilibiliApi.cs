using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Linq;
using Newtonsoft.Json;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core
{
    public class BilibiliApi
    {
        private readonly HttpClient _httpClient;
        private readonly FallbackVideoParser _fallbackParser;
        private string _currentCookie;

        public BilibiliApi()
        {
            _httpClient = new HttpClient();
            _fallbackParser = new FallbackVideoParser();
            SetupHttpClient();

            Logger.Instance.Info($"备用解析器状态: {_fallbackParser.GetParserStatus()}");
        }
        
        private void SetupHttpClient()
        {
            // 设置基本的请求头
            _httpClient.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Referer", "https://www.bilibili.com/");
        }
        
        public void SetCookie(string cookie)
        {
            _currentCookie = cookie;
            _httpClient.DefaultRequestHeaders.Remove("Cookie");
            if (!string.IsNullOrEmpty(cookie))
            {
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
            }
        }


        
        public async Task<VideoInfo> GetVideoInfoAsync(string url, bool bypassPayment = false)
        {
            try
            {
                Logger.Instance.Info($"开始解析视频链接: {url}");

                var urlInfo = await ExtractVideoUrlInfoAsync(url);
                if (string.IsNullOrEmpty(urlInfo.VideoId))
                {
                    Logger.Instance.Error($"无效的视频链接: {url}");
                    throw new ArgumentException("无效的视频链接");
                }

                Logger.Instance.Info($"提取到视频ID: {urlInfo.VideoId}, 页面: {urlInfo.Page}");

                // 获取视频基本信息
                var videoInfo = await GetVideoBasicInfoAsync(urlInfo.VideoId);
                Logger.Instance.Info($"获取视频基本信息成功: {videoInfo.Title}");

                // 设置当前页面信息
                videoInfo.CurrentPage = urlInfo.Page;

                // 如果是合集，设置合集标题
                if (videoInfo.IsCollection)
                {
                    // 保留原始标题作为合集标题，用于文件夹命名
                    videoInfo.CollectionTitle = videoInfo.Title;
                    Logger.Instance.Info($"检测到合集视频，合集标题: {videoInfo.CollectionTitle}");

                    // 如果指定了页面，更新标题为当前页面的标题
                    if (videoInfo.Pages.Count >= urlInfo.Page)
                    {
                        var currentPage = videoInfo.Pages.FirstOrDefault(p => p.Page == urlInfo.Page);
                        if (currentPage != null)
                        {
                            // 设置当前页面标题
                            videoInfo.Title = currentPage.Part;
                            Logger.Instance.Info($"当前页面标题: {videoInfo.Title} (P{urlInfo.Page})");
                        }
                    }
                }

                // 获取视频播放信息（传入页面参数和绕过标志）
                var playInfo = await GetVideoPlayInfoAsync(urlInfo.VideoId, urlInfo.Page, bypassPayment);
                Logger.Instance.Info("获取视频播放信息成功");

                // 合并信息
                MergeVideoInfo(videoInfo, playInfo);
                Logger.Instance.Info($"视频信息解析完成，可用清晰度: {videoInfo.AvailableQualities.Count}");

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"主要解析方法失败: {ex.Message}", ex);

                // 尝试使用备用解析器
                if (_fallbackParser.HasAvailableParsers())
                {
                    Logger.Instance.Info("尝试使用备用解析器...");
                    try
                    {
                        var fallbackResult = await _fallbackParser.ParseVideoAsync(url);
                        if (fallbackResult != null)
                        {
                            Logger.Instance.Info("备用解析器解析成功");
                            return fallbackResult;
                        }
                    }
                    catch (Exception fallbackEx)
                    {
                        Logger.Instance.Error($"备用解析器也失败了: {fallbackEx.Message}", fallbackEx);
                    }
                }

                throw new Exception($"所有解析方法均失败。主要错误: {ex.Message}", ex);
            }
        }
        
        private async Task<VideoUrlInfo> ExtractVideoUrlInfoAsync(string url)
        {
            try
            {
                var urlInfo = new VideoUrlInfo();
                Logger.Instance.Info($"开始解析URL: {url}");

                // 处理短链接
                if (url.Contains("b23.tv"))
                {
                    url = await ResolveShortUrlAsync(url);
                    Logger.Instance.Info($"短链接解析结果: {url}");
                }

                // 扩展的视频ID提取模式
                var videoIdPatterns = new[]
                {
                    @"bilibili\.com/video/(BV[A-Za-z0-9]+)",     // BV号
                    @"bilibili\.com/video/av(\d+)",              // AV号
                    @"bilibili\.com/bangumi/play/ss(\d+)",       // 番剧SS号
                    @"bilibili\.com/bangumi/play/ep(\d+)",       // 番剧EP号
                    @"(?:^|[^A-Za-z0-9])(BV[A-Za-z0-9]{10})",   // 直接BV号（更严格）
                    @"(?:^|[^A-Za-z0-9])av(\d+)",               // 直接AV号
                    @"(?:^|[^A-Za-z0-9])ss(\d+)",               // 直接SS号
                    @"(?:^|[^A-Za-z0-9])ep(\d+)",               // 直接EP号
                };

                foreach (var pattern in videoIdPatterns)
                {
                    var match = Regex.Match(url, pattern, RegexOptions.IgnoreCase);
                    if (match.Success)
                    {
                        var videoId = match.Groups[1].Value;

                        // 根据不同类型处理
                        if (pattern.Contains("bangumi/play/ss") || pattern.Contains("ss("))
                        {
                            urlInfo.VideoId = "ss" + videoId;
                            urlInfo.IsCollection = true;
                            Logger.Instance.Info($"检测到番剧SS号: ss{videoId}");
                        }
                        else if (pattern.Contains("bangumi/play/ep") || pattern.Contains("ep("))
                        {
                            urlInfo.VideoId = "ep" + videoId;
                            Logger.Instance.Info($"检测到番剧EP号: ep{videoId}");
                        }
                        else if (Regex.IsMatch(videoId, @"^\d+$"))
                        {
                            // AV号，转换为BV号
                            var avNumber = long.Parse(videoId);
                            urlInfo.VideoId = AvToBv(avNumber);
                            Logger.Instance.Info($"AV号转换: av{avNumber} -> {urlInfo.VideoId}");
                        }
                        else
                        {
                            // BV号
                            if (!videoId.StartsWith("BV", StringComparison.OrdinalIgnoreCase))
                            {
                                videoId = "BV" + videoId;
                            }
                            urlInfo.VideoId = videoId;
                            Logger.Instance.Info($"提取到BV号: {videoId}");
                        }
                        break;
                    }
                }

                // 如果没有匹配到，尝试更宽松的匹配
                if (string.IsNullOrEmpty(urlInfo.VideoId))
                {
                    // 尝试从URL中提取任何看起来像BV号的字符串
                    var looseBvMatch = Regex.Match(url, @"(BV[A-Za-z0-9]{10})", RegexOptions.IgnoreCase);
                    if (looseBvMatch.Success)
                    {
                        urlInfo.VideoId = looseBvMatch.Groups[1].Value;
                        Logger.Instance.Info($"宽松匹配到BV号: {urlInfo.VideoId}");
                    }
                }

                // 提取页面参数
                var pageMatch = Regex.Match(url, @"[?&]p=(\d+)", RegexOptions.IgnoreCase);
                if (pageMatch.Success && int.TryParse(pageMatch.Groups[1].Value, out int page))
                {
                    urlInfo.Page = page;
                    Logger.Instance.Info($"从URL中提取到页面参数: p={page}");
                }

                if (string.IsNullOrEmpty(urlInfo.VideoId))
                {
                    throw new ArgumentException($"无法从URL中提取视频ID: {url}");
                }

                return urlInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"URL解析失败: {ex.Message}", ex);
                throw new Exception($"解析视频URL信息失败: {ex.Message}", ex);
            }
        }

        private async Task<string> ResolveShortUrlAsync(string shortUrl)
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Head, shortUrl);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

                var response = await _httpClient.SendAsync(request);

                // 获取重定向后的URL
                var finalUrl = response.RequestMessage?.RequestUri?.ToString() ?? shortUrl;
                Logger.Instance.Info($"短链接 {shortUrl} 解析为: {finalUrl}");

                return finalUrl;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"短链接解析失败: {ex.Message}，使用原URL");
                return shortUrl;
            }
        }

        private string AvToBv(long av)
        {
            // AV号转BV号算法（基于B站官方算法）
            const string table = "fZodR9XQDSUm21yCkr6zBqiveYah8bt4xsWpHnJE7jL5VG3guMTKNPAwcF";
            const long xor = 177451812;
            const long add = 8728348608;

            var x = (av ^ xor) + add;
            var r = new char[10] { 'B', 'V', '1', '0', '0', '0', '0', '0', '0', '0' };
            var s = new int[] { 11, 10, 3, 8, 4, 6 };

            for (int i = 0; i < 6; i++)
            {
                r[s[i]] = table[(int)(x / (long)Math.Pow(58, i) % 58)];
            }

            return new string(r);
        }

        private class VideoUrlInfo
        {
            public string VideoId { get; set; }
            public int Page { get; set; } = 1; // 默认第一页
            public bool IsCollection { get; set; } = false; // 是否为合集/番剧
        }
        
        private async Task<VideoInfo> GetVideoBasicInfoAsync(string videoId)
        {
            // 根据视频ID类型选择不同的API
            string apiUrl;
            if (videoId.StartsWith("ss"))
            {
                // 番剧SS号
                var ssId = videoId.Substring(2);
                apiUrl = $"https://api.bilibili.com/pgc/view/web/season?season_id={ssId}";
                return await GetBangumiInfoAsync(apiUrl, videoId);
            }
            else if (videoId.StartsWith("ep"))
            {
                // 番剧EP号
                var epId = videoId.Substring(2);
                apiUrl = $"https://api.bilibili.com/pgc/view/web/season?ep_id={epId}";
                return await GetBangumiInfoAsync(apiUrl, videoId);
            }
            else
            {
                // 普通视频BV号或AV号
                if (videoId.StartsWith("av"))
                {
                    apiUrl = $"https://api.bilibili.com/x/web-interface/view?aid={videoId.Substring(2)}";
                }
                else
                {
                    apiUrl = $"https://api.bilibili.com/x/web-interface/view?bvid={videoId}";
                }
            }

            try
            {
                var response = await _httpClient.GetStringAsync(apiUrl);
                var jsonData = JsonConvert.DeserializeObject<dynamic>(response);

                if (jsonData.code != 0)
                {
                    throw new Exception($"API错误 (code: {jsonData.code}): {jsonData.message}");
                }

                var data = jsonData.data;
                var videoInfo = new VideoInfo
                {
                    VideoId = videoId,
                    Title = data.title?.ToString() ?? "未知标题",
                    Description = data.desc?.ToString() ?? "",
                    Author = data.owner?.name?.ToString() ?? "未知作者",
                    AuthorId = data.owner?.mid?.ToString() ?? "",
                    Duration = Convert.ToInt64(data.duration ?? 0),
                    CoverUrl = data.pic?.ToString() ?? "",
                    OriginalUrl = $"https://www.bilibili.com/video/{videoId}",
                    PublishTime = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(data.pubdate ?? 0)).DateTime
                };

                // 检查是否需要付费
                if (data.rights != null)
                {
                    videoInfo.IsPaymentRequired = Convert.ToBoolean(data.rights.pay ?? 0);
                    videoInfo.IsVip = Convert.ToBoolean(data.rights.hd5 ?? 0);
                }

                // 处理页面信息
                if (data.pages != null)
                {
                    var pagesArray = data.pages as Newtonsoft.Json.Linq.JArray;
                    if (pagesArray != null)
                    {
                        foreach (var pageItem in pagesArray)
                        {
                            var page = new VideoPage
                            {
                                Page = Convert.ToInt32(pageItem["page"] ?? 1),
                                Cid = Convert.ToInt64(pageItem["cid"] ?? 0),
                                Part = pageItem["part"]?.ToString() ?? $"第{pageItem["page"]}话",
                                Duration = Convert.ToInt64(pageItem["duration"] ?? 0),
                                Dimension = pageItem["dimension"]?.ToString() ?? ""
                            };
                            videoInfo.Pages.Add(page);
                        }

                        // 如果有多个页面，标记为合集
                        if (pagesArray.Count > 1)
                        {
                            videoInfo.IsCollection = true;
                        }
                    }
                }

                return videoInfo;
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"网络请求失败: {ex.Message}", ex);
            }
            catch (JsonException ex)
            {
                throw new Exception($"JSON解析失败: {ex.Message}", ex);
            }
        }
        
        public async Task<dynamic> GetVideoPlayInfoAsync(string videoId, int page = 1, bool bypassPayment = false)
        {
            try
            {
                // 首先获取cid
                var cidUrl = $"https://api.bilibili.com/x/player/pagelist?bvid={videoId}";
                var cidResponse = await _httpClient.GetStringAsync(cidUrl);
                var cidData = JsonConvert.DeserializeObject<dynamic>(cidResponse);

                if (cidData.code != 0)
                {
                    throw new Exception($"获取CID失败 (code: {cidData.code}): {cidData.message}");
                }

                // 根据页面参数选择正确的cid
                var pages = cidData.data as Newtonsoft.Json.Linq.JArray;
                if (pages == null || pages.Count == 0)
                {
                    throw new Exception("未找到视频页面信息");
                }

                // 确保页面索引有效（页面从1开始，数组从0开始）
                var pageIndex = Math.Max(0, Math.Min(page - 1, pages.Count - 1));
                var cid = pages[pageIndex]["cid"];

                Logger.Instance.Info($"选择页面 {page}，CID: {cid}");

                // 如果启用付费绕过，尝试多种策略
                if (bypassPayment)
                {
                    Logger.Instance.Info("启用付费绕过模式，尝试多种策略");
                    return await TryBypassPaymentAsync(videoId, cid.ToString());
                }

                // 获取播放信息 - 使用更新的参数
                var playUrl = $"https://api.bilibili.com/x/player/playurl?bvid={videoId}&cid={cid}&qn=120&fnval=4048&fourk=1&try_look=1";

                Logger.Instance.Info($"请求播放信息URL: {playUrl}");

                // 直接使用HttpClient，因为默认请求头已经设置好了
                var responseContent = await _httpClient.GetStringAsync(playUrl);
                var playData = JsonConvert.DeserializeObject<dynamic>(responseContent);

                Logger.Instance.Info($"播放信息API响应: code={playData?.code}, message={playData?.message}");

                return playData;
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"获取播放信息失败: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"解析播放信息失败: {ex.Message}", ex);
            }
        }

        private async Task<dynamic> TryBypassPaymentAsync(string videoId, string cid)
        {
            var strategies = new List<Func<Task<dynamic>>>
            {
                // 策略1: 使用试看参数
                () => TryBypassStrategy1(videoId, cid),
                // 策略2: 使用移动端API
                () => TryBypassStrategy2(videoId, cid),
                // 策略3: 使用不同的fnval参数
                () => TryBypassStrategy3(videoId, cid),
                // 策略4: 使用海外代理参数
                () => TryBypassStrategy4(videoId, cid)
            };

            foreach (var strategy in strategies)
            {
                try
                {
                    Logger.Instance.Info("尝试付费绕过策略...");
                    var result = await strategy();

                    if (result?.code == 0 && result?.data != null)
                    {
                        Logger.Instance.Info("付费绕过成功！");
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Warning($"绕过策略失败: {ex.Message}");
                }
            }

            Logger.Instance.Warning("所有付费绕过策略均失败，返回原始错误");
            throw new Exception("付费绕过失败，无法获取视频播放链接");
        }

        private async Task<dynamic> TryBypassStrategy1(string videoId, string cid)
        {
            // 策略1: 使用试看参数和不同的qn值
            var playUrl = $"https://api.bilibili.com/x/player/playurl?bvid={videoId}&cid={cid}&qn=80&fnval=16&try_look=1&platform=html5";
            Logger.Instance.Info($"策略1 - 试看模式: {playUrl}");

            var responseContent = await _httpClient.GetStringAsync(playUrl);
            return JsonConvert.DeserializeObject<dynamic>(responseContent);
        }

        private async Task<dynamic> TryBypassStrategy2(string videoId, string cid)
        {
            // 策略2: 模拟移动端请求
            var originalUserAgent = _httpClient.DefaultRequestHeaders.UserAgent.ToString();

            try
            {
                _httpClient.DefaultRequestHeaders.Remove("User-Agent");
                _httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15");

                var playUrl = $"https://api.bilibili.com/x/player/playurl?bvid={videoId}&cid={cid}&qn=64&fnval=1&platform=ios";
                Logger.Instance.Info($"策略2 - 移动端模式: {playUrl}");

                var responseContent = await _httpClient.GetStringAsync(playUrl);
                return JsonConvert.DeserializeObject<dynamic>(responseContent);
            }
            finally
            {
                // 恢复原始User-Agent
                _httpClient.DefaultRequestHeaders.Remove("User-Agent");
                _httpClient.DefaultRequestHeaders.Add("User-Agent", originalUserAgent);
            }
        }

        private async Task<dynamic> TryBypassStrategy3(string videoId, string cid)
        {
            // 策略3: 使用不同的fnval参数组合
            var playUrl = $"https://api.bilibili.com/x/player/playurl?bvid={videoId}&cid={cid}&qn=32&fnval=1&otype=json";
            Logger.Instance.Info($"策略3 - 传统格式: {playUrl}");

            var responseContent = await _httpClient.GetStringAsync(playUrl);
            return JsonConvert.DeserializeObject<dynamic>(responseContent);
        }

        private async Task<dynamic> TryBypassStrategy4(string videoId, string cid)
        {
            // 策略4: 添加特殊请求头
            var request = new HttpRequestMessage(HttpMethod.Get,
                $"https://api.bilibili.com/x/player/playurl?bvid={videoId}&cid={cid}&qn=80&fnval=16&session=");

            request.Headers.Add("Origin", "https://www.bilibili.com");
            request.Headers.Add("Referer", $"https://www.bilibili.com/video/{videoId}");

            Logger.Instance.Info($"策略4 - 特殊请求头模式");

            var response = await _httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<dynamic>(responseContent);
        }

        private void MergeVideoInfo(VideoInfo videoInfo, dynamic playInfo)
        {
            Logger.Instance.Info($"开始合并视频播放信息，API响应码: {playInfo.code}");

            if (playInfo.code != 0)
            {
                Logger.Instance.Warning($"获取播放信息失败，错误码: {playInfo.code}, 消息: {playInfo.message}");

                // 如果获取播放信息失败，可能是权限问题
                if (playInfo.code == -10403)
                {
                    videoInfo.IsPaymentRequired = true;
                    Logger.Instance.Info("检测到付费视频");
                }
                return;
            }

            var data = playInfo.data;
            Logger.Instance.Info("播放信息数据获取成功，开始解析清晰度");

            // 解析可用的清晰度
            if (data.dash != null)
            {
                Logger.Instance.Info("检测到DASH格式，开始解析DASH清晰度");
                // DASH格式（音视频分离）
                ParseDashQualities(videoInfo, data.dash);
            }
            else if (data.durl != null)
            {
                Logger.Instance.Info("检测到传统格式，开始解析传统清晰度");
                // 传统格式（音视频合并）
                ParseTraditionalQualities(videoInfo, data.durl);
            }
            else
            {
                Logger.Instance.Warning("未找到DASH或DURL数据，无法解析清晰度");
            }

            Logger.Instance.Info($"清晰度解析完成，共找到 {videoInfo.AvailableQualities.Count} 个清晰度选项");
        }
        
        private void ParseDashQualities(VideoInfo videoInfo, dynamic dash)
        {
            Logger.Instance.Info("开始解析DASH清晰度信息");

            if (dash.video != null)
            {
                var videoArray = dash.video as Newtonsoft.Json.Linq.JArray;
                Logger.Instance.Info($"找到 {videoArray?.Count ?? 0} 个视频流");

                foreach (var video in dash.video)
                {
                    try
                    {
                        var qualityId = Convert.ToInt32(video.id);
                        var qualityName = GetQualityName(qualityId);

                        Logger.Instance.Info($"解析视频流: ID={qualityId}, 名称={qualityName}");

                        var quality = new VideoQuality
                        {
                            Quality = qualityId,
                            QualityName = qualityName,
                            VideoUrl = video.baseUrl?.ToString(),
                            FileSize = Convert.ToInt64(video.bandwidth ?? 0),
                            Format = "mp4",
                            Codec = video.codecs?.ToString()
                        };

                        // 查找对应的音频流
                        if (dash.audio != null && dash.audio.Count > 0)
                        {
                            quality.AudioUrl = dash.audio[0].baseUrl?.ToString();
                            Logger.Instance.Info($"为清晰度 {qualityName} 找到音频流");
                        }
                        else
                        {
                            Logger.Instance.Warning($"清晰度 {qualityName} 没有找到音频流");
                        }

                        videoInfo.AvailableQualities.Add(quality);
                        Logger.Instance.Info($"成功添加清晰度选项: {qualityName}");
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"解析视频流时出错: {ex.Message}", ex);
                    }
                }
            }
            else
            {
                Logger.Instance.Warning("DASH数据中没有找到video字段");
            }
        }
        
        private void ParseTraditionalQualities(VideoInfo videoInfo, dynamic durl)
        {
            Logger.Instance.Info("开始解析传统格式清晰度信息");

            var durlArray = durl as Newtonsoft.Json.Linq.JArray;
            Logger.Instance.Info($"找到 {durlArray?.Count ?? 0} 个传统格式流");

            foreach (var item in durl)
            {
                try
                {
                    var qualityId = Convert.ToInt32(item.quality ?? 80);
                    var qualityName = GetQualityName(qualityId);

                    Logger.Instance.Info($"解析传统流: ID={qualityId}, 名称={qualityName}");

                    var quality = new VideoQuality
                    {
                        Quality = qualityId,
                        QualityName = qualityName,
                        VideoUrl = item.url?.ToString(),
                        FileSize = Convert.ToInt64(item.size ?? 0),
                        Format = "flv"
                    };

                    videoInfo.AvailableQualities.Add(quality);
                    Logger.Instance.Info($"成功添加传统格式清晰度选项: {qualityName}");
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"解析传统格式流时出错: {ex.Message}", ex);
                }
            }
        }

        private async Task<VideoInfo> GetBangumiInfoAsync(string apiUrl, string videoId)
        {
            try
            {
                Logger.Instance.Info($"开始解析番剧: {videoId}");

                // 使用多种策略解析番剧
                var strategies = new[]
                {
                    () => TryWebSeasonApiAsync(videoId),
                    () => TryWebSectionApiAsync(videoId),
                    () => TryAppSeasonApiAsync(videoId),
                    () => TryTvSeasonApiAsync(videoId),
                    () => TryWebPageParsingAsync(videoId)
                };

                foreach (var strategy in strategies)
                {
                    try
                    {
                        var result = await strategy();
                        if (result != null)
                        {
                            Logger.Instance.Info($"番剧解析成功: {result.Title}");
                            return result;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Warning($"番剧解析策略失败: {ex.Message}");
                    }
                }

                // 所有策略都失败，返回基本信息
                Logger.Instance.Warning("所有番剧解析策略都失败，返回基本信息");
                return CreateFallbackBangumiInfo(videoId);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取番剧信息失败: {ex.Message}", ex);
                throw new Exception($"获取番剧信息失败: {ex.Message}", ex);
            }
        }

        private async Task<VideoInfo> TryAlternativeBangumiApiAsync(string videoId)
        {
            var ssId = videoId.StartsWith("ss") ? videoId.Substring(2) :
                      videoId.StartsWith("ep") ? videoId.Substring(2) : videoId;

            // 尝试不同的API策略
            var strategies = new[]
            {
                // 策略1: 使用移动端API
                () => TryMobileBangumiApiAsync(ssId, videoId),
                // 策略2: 使用网页端API
                () => TryWebBangumiApiAsync(ssId, videoId),
                // 策略3: 通过网页解析
                () => TryWebPageParsingAsync(videoId),
                // 策略4: 使用搜索API
                () => TrySearchBangumiAsync(ssId, videoId)
            };

            foreach (var strategy in strategies)
            {
                try
                {
                    var result = await strategy();
                    if (result != null)
                    {
                        Logger.Instance.Info("番剧解析成功");
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Warning($"番剧解析策略失败: {ex.Message}");
                }
            }

            // 如果所有API都失败，创建一个基本的VideoInfo
            Logger.Instance.Warning("所有番剧API都失败，创建基本信息");
            return new VideoInfo
            {
                VideoId = videoId,
                Title = $"番剧 {videoId}",
                Description = "无法获取详细信息",
                Author = "bilibili",
                OriginalUrl = $"https://www.bilibili.com/bangumi/play/{videoId}",
                IsCollection = true,
                Pages = new List<VideoPage>
                {
                    new VideoPage
                    {
                        Page = 1,
                        Cid = 0,
                        Part = $"番剧 {videoId}",
                        Duration = 0
                    }
                }
            };
        }

        private VideoInfo CreateBangumiVideoInfo(dynamic result, string videoId)
        {
            var videoInfo = new VideoInfo
            {
                VideoId = videoId,
                Title = result.title?.ToString() ?? $"番剧 {videoId}",
                Description = result.evaluate?.ToString() ?? "",
                Author = result.up_info?.uname?.ToString() ?? "bilibili",
                Duration = 0,
                OriginalUrl = $"https://www.bilibili.com/bangumi/play/{videoId}",
                IsCollection = true
            };

            // 处理分集信息
            if (result.episodes != null)
            {
                var episodes = result.episodes as Newtonsoft.Json.Linq.JArray;
                if (episodes != null)
                {
                    foreach (var episode in episodes)
                    {
                        var pageInfo = new VideoPage
                        {
                            Page = (int)(episode["id"] ?? episodes.IndexOf(episode) + 1),
                            Cid = (long)(episode["cid"] ?? 0),
                            Part = episode["long_title"]?.ToString() ?? episode["title"]?.ToString() ?? $"第{episodes.IndexOf(episode) + 1}话",
                            Duration = (long)(episode["duration"] ?? 0) / 1000
                        };

                        videoInfo.Pages.Add(pageInfo);
                    }
                }
            }

            if (videoInfo.Pages.Count == 0)
            {
                videoInfo.Pages.Add(new VideoPage
                {
                    Page = 1,
                    Cid = 0,
                    Part = videoInfo.Title,
                    Duration = 0
                });
            }

            return videoInfo;
        }

        private VideoInfo CreateFallbackBangumiInfo(string videoId)
        {
            return new VideoInfo
            {
                VideoId = videoId,
                Title = $"番剧 {videoId}",
                Description = "无法获取详细信息，可能是地区限制或需要登录",
                Author = "bilibili",
                OriginalUrl = $"https://www.bilibili.com/bangumi/play/{videoId}",
                IsCollection = true,
                Pages = new List<VideoPage>
                {
                    new VideoPage
                    {
                        Page = 1,
                        Cid = 0,
                        Part = $"番剧 {videoId}",
                        Duration = 0
                    }
                }
            };
        }

        private async Task<VideoInfo?> TryWebSeasonApiAsync(string videoId)
        {
            try
            {
                Logger.Instance.Info("尝试Web Season API");
                var ssId = ExtractSeasonId(videoId);
                var epId = ExtractEpisodeId(videoId);

                var apiUrl = !string.IsNullOrEmpty(ssId)
                    ? $"https://api.bilibili.com/pgc/view/web/season?season_id={ssId}"
                    : $"https://api.bilibili.com/pgc/view/web/season?ep_id={epId}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                request.Headers.Add("Referer", "https://www.bilibili.com/");
                request.Headers.Add("Accept", "application/json, text/plain, */*");
                request.Headers.Add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                Logger.Instance.Info($"Web Season API响应: {content.Substring(0, Math.Min(300, content.Length))}...");

                var jsonData = JsonConvert.DeserializeObject<dynamic>(content);
                if (jsonData.code == 0 && jsonData.result != null)
                {
                    return ParseWebSeasonResponse(jsonData.result, videoId);
                }

                Logger.Instance.Warning($"Web Season API失败: code={jsonData.code}, message={jsonData.message}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"Web Season API异常: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> TryWebSectionApiAsync(string videoId)
        {
            try
            {
                Logger.Instance.Info("尝试Web Section API");
                var ssId = ExtractSeasonId(videoId);
                if (string.IsNullOrEmpty(ssId)) return null;

                var apiUrl = $"https://api.bilibili.com/pgc/web/season/section?season_id={ssId}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                request.Headers.Add("Referer", "https://www.bilibili.com/");

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                var jsonData = JsonConvert.DeserializeObject<dynamic>(content);
                if (jsonData.code == 0 && jsonData.result != null)
                {
                    return ParseWebSectionResponse(jsonData.result, videoId, ssId);
                }

                Logger.Instance.Warning($"Web Section API失败: {jsonData.message}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"Web Section API异常: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> TryAppSeasonApiAsync(string videoId)
        {
            try
            {
                Logger.Instance.Info("尝试App Season API");
                var ssId = ExtractSeasonId(videoId);
                if (string.IsNullOrEmpty(ssId)) return null;

                var apiUrl = $"https://api.bilibili.com/pgc/view/app/season?season_id={ssId}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Add("User-Agent", "Bilibili Freedoooooom/MarkII");
                request.Headers.Add("Referer", "https://www.bilibili.com/");

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                var jsonData = JsonConvert.DeserializeObject<dynamic>(content);
                if (jsonData.code == 0 && jsonData.result != null)
                {
                    return ParseAppSeasonResponse(jsonData.result, videoId);
                }

                Logger.Instance.Warning($"App Season API失败: {jsonData.message}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"App Season API异常: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> TryTvSeasonApiAsync(string videoId)
        {
            try
            {
                Logger.Instance.Info("尝试TV Season API");
                var ssId = ExtractSeasonId(videoId);
                if (string.IsNullOrEmpty(ssId)) return null;

                var apiUrl = $"https://api.snm0516.aisee.tv/pgc/view/app/season?season_id={ssId}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Add("User-Agent", "Bilibili Freedoooooom/MarkII");

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                var jsonData = JsonConvert.DeserializeObject<dynamic>(content);
                if (jsonData.code == 0 && jsonData.result != null)
                {
                    return ParseAppSeasonResponse(jsonData.result, videoId);
                }

                Logger.Instance.Warning($"TV Season API失败: {jsonData.message}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"TV Season API异常: {ex.Message}");
                return null;
            }
        }

        private string ExtractSeasonId(string videoId)
        {
            if (videoId.StartsWith("ss"))
                return videoId.Substring(2);
            return "";
        }

        private string ExtractEpisodeId(string videoId)
        {
            if (videoId.StartsWith("ep"))
                return videoId.Substring(2);
            return "";
        }

        private async Task<VideoInfo?> TryMobileBangumiApiAsync(string ssId, string videoId)
        {
            try
            {
                Logger.Instance.Info("尝试移动端番剧API");
                var apiUrl = $"https://api.bilibili.com/pgc/view/app/season?season_id={ssId}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Add("User-Agent", "Bilibili Freedoooooom/MarkII");
                request.Headers.Add("Referer", "https://www.bilibili.com/");

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();
                var jsonData = JsonConvert.DeserializeObject<dynamic>(content);

                if (jsonData.code == 0 && jsonData.result != null)
                {
                    return CreateBangumiVideoInfo(jsonData.result, videoId);
                }

                Logger.Instance.Warning($"移动端API失败: {jsonData.message}");
                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"移动端API异常: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> TryWebBangumiApiAsync(string ssId, string videoId)
        {
            try
            {
                Logger.Instance.Info("尝试网页端番剧API");
                var apiUrls = new[]
                {
                    $"https://api.bilibili.com/pgc/web/season/section?season_id={ssId}",
                    $"https://bangumi.bilibili.com/view/web_api/season?season_id={ssId}",
                    $"https://api.bilibili.com/pgc/view/web/season?season_id={ssId}&ep_id="
                };

                foreach (var apiUrl in apiUrls)
                {
                    try
                    {
                        var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                        request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                        request.Headers.Add("Referer", "https://www.bilibili.com/");
                        request.Headers.Add("Origin", "https://www.bilibili.com");

                        var response = await _httpClient.SendAsync(request);
                        var content = await response.Content.ReadAsStringAsync();
                        var jsonData = JsonConvert.DeserializeObject<dynamic>(content);

                        if (jsonData.code == 0 && jsonData.result != null)
                        {
                            return CreateBangumiVideoInfo(jsonData.result, videoId);
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Warning($"网页端API请求失败: {ex.Message}");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"网页端API异常: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> TryWebPageParsingAsync(string videoId)
        {
            try
            {
                Logger.Instance.Info("尝试网页解析番剧信息");
                var pageUrl = $"https://www.bilibili.com/bangumi/play/{videoId}";

                var request = new HttpRequestMessage(HttpMethod.Get, pageUrl);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

                var response = await _httpClient.SendAsync(request);
                var html = await response.Content.ReadAsStringAsync();

                // 尝试从HTML中提取番剧信息
                var titleMatch = System.Text.RegularExpressions.Regex.Match(html, @"<title>([^<]+)</title>");
                var title = titleMatch.Success ? titleMatch.Groups[1].Value.Replace("_哔哩哔哩_bilibili", "").Trim() : $"番剧 {videoId}";

                // 创建基本的番剧信息
                return new VideoInfo
                {
                    VideoId = videoId,
                    Title = title,
                    Description = "通过网页解析获取",
                    Author = "bilibili",
                    OriginalUrl = pageUrl,
                    IsCollection = true,
                    Pages = new List<VideoPage>
                    {
                        new VideoPage
                        {
                            Page = 1,
                            Cid = 0,
                            Part = title,
                            Duration = 0
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"网页解析异常: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> TrySearchBangumiAsync(string ssId, string videoId)
        {
            try
            {
                Logger.Instance.Info("尝试搜索API获取番剧信息");
                var searchUrl = $"https://api.bilibili.com/x/web-interface/search/type?search_type=media_bangumi&keyword=ss{ssId}";

                var response = await _httpClient.GetStringAsync(searchUrl);
                var jsonData = JsonConvert.DeserializeObject<dynamic>(response);

                if (jsonData.code == 0 && jsonData.data?.result != null)
                {
                    var results = jsonData.data.result as Newtonsoft.Json.Linq.JArray;
                    if (results != null && results.Count > 0)
                    {
                        var firstResult = results[0];
                        return new VideoInfo
                        {
                            VideoId = videoId,
                            Title = firstResult["title"]?.ToString() ?? $"番剧 {videoId}",
                            Description = firstResult["desc"]?.ToString() ?? "",
                            Author = "bilibili",
                            OriginalUrl = $"https://www.bilibili.com/bangumi/play/{videoId}",
                            IsCollection = true,
                            Pages = new List<VideoPage>
                            {
                                new VideoPage
                                {
                                    Page = 1,
                                    Cid = 0,
                                    Part = firstResult["title"]?.ToString() ?? $"番剧 {videoId}",
                                    Duration = 0
                                }
                            }
                        };
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"搜索API异常: {ex.Message}");
                return null;
            }
        }

        private VideoInfo ParseWebSeasonResponse(dynamic result, string videoId)
        {
            var videoInfo = new VideoInfo
            {
                VideoId = videoId,
                Title = result.title?.ToString() ?? $"番剧 {videoId}",
                Description = result.evaluate?.ToString() ?? "",
                Author = result.up_info?.uname?.ToString() ?? "bilibili",
                OriginalUrl = $"https://www.bilibili.com/bangumi/play/{videoId}",
                IsCollection = true
            };

            // 解析正片episodes
            if (result.episodes != null)
            {
                var episodes = result.episodes as Newtonsoft.Json.Linq.JArray;
                if (episodes != null)
                {
                    foreach (var episode in episodes)
                    {
                        var pageInfo = new VideoPage
                        {
                            Page = (int)(episode["id"] ?? episodes.IndexOf(episode) + 1),
                            Cid = (long)(episode["cid"] ?? 0),
                            Part = episode["long_title"]?.ToString() ?? episode["title"]?.ToString() ?? $"第{episodes.IndexOf(episode) + 1}话",
                            Duration = (long)(episode["duration"] ?? 0) / 1000
                        };
                        videoInfo.Pages.Add(pageInfo);
                    }
                }
            }

            // 解析section中的episodes
            if (videoInfo.Pages.Count == 0 && result.section != null)
            {
                var sections = result.section as Newtonsoft.Json.Linq.JArray;
                if (sections != null)
                {
                    int pageIndex = 1;
                    foreach (var section in sections)
                    {
                        if (section["episodes"] != null)
                        {
                            var sectionEpisodes = section["episodes"] as Newtonsoft.Json.Linq.JArray;
                            if (sectionEpisodes != null)
                            {
                                foreach (var episode in sectionEpisodes)
                                {
                                    var pageInfo = new VideoPage
                                    {
                                        Page = pageIndex++,
                                        Cid = (long)(episode["cid"] ?? 0),
                                        Part = episode["long_title"]?.ToString() ?? episode["title"]?.ToString() ?? $"第{pageIndex-1}话",
                                        Duration = (long)(episode["duration"] ?? 0) / 1000
                                    };
                                    videoInfo.Pages.Add(pageInfo);
                                }
                            }
                        }
                    }
                }
            }

            if (videoInfo.Pages.Count == 0)
            {
                videoInfo.Pages.Add(new VideoPage
                {
                    Page = 1,
                    Cid = 0,
                    Part = videoInfo.Title,
                    Duration = 0
                });
            }

            Logger.Instance.Info($"Web Season解析完成: {videoInfo.Title}, 共{videoInfo.Pages.Count}集");
            return videoInfo;
        }

        private VideoInfo ParseWebSectionResponse(dynamic result, string videoId, string ssId)
        {
            var videoInfo = new VideoInfo
            {
                VideoId = videoId,
                Title = $"番剧 ss{ssId}",
                Description = "通过Section API获取",
                Author = "bilibili",
                OriginalUrl = $"https://www.bilibili.com/bangumi/play/{videoId}",
                IsCollection = true
            };

            // 解析main_section
            if (result.main_section?.episodes != null)
            {
                var episodes = result.main_section.episodes as Newtonsoft.Json.Linq.JArray;
                if (episodes != null)
                {
                    foreach (var episode in episodes)
                    {
                        var pageInfo = new VideoPage
                        {
                            Page = (int)(episode["id"] ?? episodes.IndexOf(episode) + 1),
                            Cid = (long)(episode["cid"] ?? 0),
                            Part = episode["long_title"]?.ToString() ?? episode["title"]?.ToString() ?? $"第{episodes.IndexOf(episode) + 1}话",
                            Duration = 0
                        };
                        videoInfo.Pages.Add(pageInfo);
                    }
                }
            }

            // 解析其他sections
            if (result.section != null)
            {
                var sections = result.section as Newtonsoft.Json.Linq.JArray;
                if (sections != null)
                {
                    foreach (var section in sections)
                    {
                        if (section["episodes"] != null)
                        {
                            var sectionEpisodes = section["episodes"] as Newtonsoft.Json.Linq.JArray;
                            if (sectionEpisodes != null)
                            {
                                foreach (var episode in sectionEpisodes)
                                {
                                    var pageInfo = new VideoPage
                                    {
                                        Page = videoInfo.Pages.Count + 1,
                                        Cid = (long)(episode["cid"] ?? 0),
                                        Part = episode["long_title"]?.ToString() ?? episode["title"]?.ToString() ?? $"特别篇{videoInfo.Pages.Count + 1}",
                                        Duration = 0
                                    };
                                    videoInfo.Pages.Add(pageInfo);
                                }
                            }
                        }
                    }
                }
            }

            if (videoInfo.Pages.Count == 0)
            {
                videoInfo.Pages.Add(new VideoPage
                {
                    Page = 1,
                    Cid = 0,
                    Part = videoInfo.Title,
                    Duration = 0
                });
            }

            Logger.Instance.Info($"Web Section解析完成: {videoInfo.Title}, 共{videoInfo.Pages.Count}集");
            return videoInfo;
        }

        private VideoInfo ParseAppSeasonResponse(dynamic result, string videoId)
        {
            // App API的响应格式与Web API类似，复用Web解析逻辑
            return ParseWebSeasonResponse(result, videoId);
        }

        private string GetQualityName(int quality)
        {
            return quality switch
            {
                120 => "4K",
                116 => "1080P60",
                112 => "1080P+",
                80 => "1080P",
                74 => "720P60",
                64 => "720P",
                32 => "480P",
                16 => "360P",
                _ => $"质量{quality}"
            };
        }
        
        public async Task<List<VideoUrl>> GetVideoUrlsAsync(string videoId, long cid, int quality = 64)
        {
            try
            {
                Logger.Instance.Info($"获取视频播放地址: videoId={videoId}, cid={cid}, quality={quality}");

                // 检查是否为番剧
                if (videoId.StartsWith("ep") || videoId.StartsWith("ss"))
                {
                    return await GetBangumiVideoUrlsAsync(videoId, cid, quality);
                }

                var apiUrl = $"https://api.bilibili.com/x/player/playurl?avid={videoId}&cid={cid}&qn={quality}&fnval=16&fnver=0&fourk=1";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                request.Headers.Add("Referer", "https://www.bilibili.com/");

                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                Logger.Instance.Info($"播放地址API响应: {responseContent.Substring(0, Math.Min(200, responseContent.Length))}...");

                var jsonData = JsonConvert.DeserializeObject<dynamic>(responseContent);

                if (jsonData.code != 0)
                {
                    Logger.Instance.Warning($"获取播放地址失败: {jsonData.message}");
                    return new List<VideoUrl>();
                }

                return ParseVideoUrls(jsonData.data, quality);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取视频播放地址异常: {ex.Message}");
                return new List<VideoUrl>();
            }
        }

        public async Task<List<VideoUrl>> GetBangumiVideoUrlsAsync(string videoId, long cid, int quality = 64)
        {
            try
            {
                Logger.Instance.Info($"获取番剧播放地址: videoId={videoId}, cid={cid}, quality={quality}");

                var epId = ExtractEpisodeId(videoId);
                if (string.IsNullOrEmpty(epId) && cid > 0)
                {
                    // 如果没有epId但有cid，尝试通过cid获取
                    epId = await GetEpIdFromCidAsync(cid);
                }

                if (string.IsNullOrEmpty(epId))
                {
                    Logger.Instance.Warning("无法获取番剧epId");
                    return new List<VideoUrl>();
                }

                // 尝试多种番剧播放地址API
                var strategies = new[]
                {
                    () => TryBangumiPlayUrlAsync(epId, cid, quality, "web"),
                    () => TryBangumiPlayUrlAsync(epId, cid, quality, "app"),
                    () => TryBangumiPlayUrlAsync(epId, cid, quality, "tv")
                };

                foreach (var strategy in strategies)
                {
                    try
                    {
                        var result = await strategy();
                        if (result.Count > 0)
                        {
                            Logger.Instance.Info($"番剧播放地址获取成功，共{result.Count}个地址");
                            return result;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Warning($"番剧播放地址获取策略失败: {ex.Message}");
                    }
                }

                Logger.Instance.Warning("所有番剧播放地址获取策略都失败");
                return new List<VideoUrl>();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取番剧播放地址异常: {ex.Message}");
                return new List<VideoUrl>();
            }
        }

        private async Task<List<VideoUrl>> TryBangumiPlayUrlAsync(string epId, long cid, int quality, string platform)
        {
            try
            {
                Logger.Instance.Info($"尝试{platform}端番剧播放地址API");

                string apiUrl;
                var request = new HttpRequestMessage(HttpMethod.Get, "");

                switch (platform.ToLower())
                {
                    case "web":
                        apiUrl = $"https://api.bilibili.com/pgc/player/web/playurl?ep_id={epId}&cid={cid}&qn={quality}&fnval=16&fnver=0&fourk=1";
                        request.RequestUri = new Uri(apiUrl);
                        request.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                        request.Headers.Add("Referer", "https://www.bilibili.com/");
                        break;
                    case "app":
                        apiUrl = $"https://api.bilibili.com/pgc/player/api/playurl?ep_id={epId}&cid={cid}&qn={quality}&fnval=16&fnver=0&fourk=1";
                        request.RequestUri = new Uri(apiUrl);
                        request.Headers.Add("User-Agent", "Bilibili Freedoooooom/MarkII");
                        break;
                    case "tv":
                        apiUrl = $"https://api.snm0516.aisee.tv/pgc/player/api/playurl?ep_id={epId}&cid={cid}&qn={quality}&fnval=16&fnver=0&fourk=1";
                        request.RequestUri = new Uri(apiUrl);
                        request.Headers.Add("User-Agent", "Bilibili Freedoooooom/MarkII");
                        break;
                    default:
                        return new List<VideoUrl>();
                }

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                Logger.Instance.Info($"{platform}端番剧播放地址API响应: {content.Substring(0, Math.Min(300, content.Length))}...");

                var jsonData = JsonConvert.DeserializeObject<dynamic>(content);

                if (jsonData.code == 0 && jsonData.result != null)
                {
                    return ParseVideoUrls(jsonData.result, quality);
                }
                else if (jsonData.code == 0 && jsonData.data != null)
                {
                    return ParseVideoUrls(jsonData.data, quality);
                }

                Logger.Instance.Warning($"{platform}端番剧播放地址API失败: code={jsonData.code}, message={jsonData.message}");
                return new List<VideoUrl>();
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"{platform}端番剧播放地址API异常: {ex.Message}");
                return new List<VideoUrl>();
            }
        }

        private List<VideoUrl> ParseVideoUrls(dynamic result, int quality)
        {
            var videoUrls = new List<VideoUrl>();

            try
            {
                // 处理DASH格式
                if (result.dash != null)
                {
                    Logger.Instance.Info("处理DASH格式视频");

                    // 视频流
                    if (result.dash.video != null)
                    {
                        foreach (var video in result.dash.video)
                        {
                            videoUrls.Add(new VideoUrl
                            {
                                Quality = (int)video.id,
                                QualityName = GetQualityName((int)video.id),
                                Url = video.baseUrl?.ToString() ?? video.base_url?.ToString() ?? "",
                                Size = (long)(video.bandwidth ?? 0),
                                Type = "video"
                            });
                        }
                    }

                    // 音频流
                    if (result.dash.audio != null)
                    {
                        foreach (var audio in result.dash.audio)
                        {
                            videoUrls.Add(new VideoUrl
                            {
                                Quality = (int)audio.id,
                                QualityName = "音频",
                                Url = audio.baseUrl?.ToString() ?? audio.base_url?.ToString() ?? "",
                                Size = (long)(audio.bandwidth ?? 0),
                                Type = "audio"
                            });
                        }
                    }
                }
                // 处理传统格式
                else if (result.durl != null)
                {
                    Logger.Instance.Info("处理传统格式视频");

                    foreach (var durl in result.durl)
                    {
                        videoUrls.Add(new VideoUrl
                        {
                            Quality = quality,
                            QualityName = GetQualityName(quality),
                            Url = durl.url?.ToString() ?? "",
                            Size = (long)(durl.size ?? 0),
                            Type = "video"
                        });
                    }
                }

                Logger.Instance.Info($"解析到 {videoUrls.Count} 个播放地址");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"解析播放地址异常: {ex.Message}");
            }

            return videoUrls;
        }

        private async Task<string> GetEpIdFromCidAsync(long cid)
        {
            try
            {
                // 这里可以实现通过cid反查epId的逻辑
                // 暂时返回空字符串
                return "";
            }
            catch
            {
                return "";
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
