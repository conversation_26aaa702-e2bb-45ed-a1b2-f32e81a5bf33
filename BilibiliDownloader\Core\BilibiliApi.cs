using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Linq;
using Newtonsoft.Json;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core
{
    public class BilibiliApi
    {
        private readonly HttpClient _httpClient;
        private string _currentCookie;
        
        public BilibiliApi()
        {
            _httpClient = new HttpClient();
            SetupHttpClient();
        }
        
        private void SetupHttpClient()
        {
            // 设置基本的请求头
            _httpClient.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Referer", "https://www.bilibili.com/");
        }
        
        public void SetCookie(string cookie)
        {
            _currentCookie = cookie;
            _httpClient.DefaultRequestHeaders.Remove("Cookie");
            if (!string.IsNullOrEmpty(cookie))
            {
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
            }
        }


        
        public async Task<VideoInfo> GetVideoInfoAsync(string url)
        {
            try
            {
                Logger.Instance.Info($"开始解析视频链接: {url}");

                var urlInfo = ExtractVideoUrlInfo(url);
                if (string.IsNullOrEmpty(urlInfo.VideoId))
                {
                    Logger.Instance.Error($"无效的视频链接: {url}");
                    throw new ArgumentException("无效的视频链接");
                }

                Logger.Instance.Info($"提取到视频ID: {urlInfo.VideoId}, 页面: {urlInfo.Page}");

                // 获取视频基本信息
                var videoInfo = await GetVideoBasicInfoAsync(urlInfo.VideoId);
                Logger.Instance.Info($"获取视频基本信息成功: {videoInfo.Title}");

                // 设置当前页面信息
                videoInfo.CurrentPage = urlInfo.Page;

                // 如果是合集且指定了页面，更新标题为当前页面的标题
                if (videoInfo.IsCollection && videoInfo.Pages.Count >= urlInfo.Page)
                {
                    var currentPage = videoInfo.Pages.FirstOrDefault(p => p.Page == urlInfo.Page);
                    if (currentPage != null)
                    {
                        videoInfo.Title = currentPage.Part;
                        Logger.Instance.Info($"更新标题为当前页面标题: {videoInfo.Title} (P{urlInfo.Page})");
                    }
                }

                // 获取视频播放信息（传入页面参数）
                var playInfo = await GetVideoPlayInfoAsync(urlInfo.VideoId, urlInfo.Page);
                Logger.Instance.Info("获取视频播放信息成功");

                // 合并信息
                MergeVideoInfo(videoInfo, playInfo);
                Logger.Instance.Info($"视频信息解析完成，可用清晰度: {videoInfo.AvailableQualities.Count}");

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取视频信息失败: {ex.Message}", ex);
                throw new Exception($"获取视频信息失败: {ex.Message}", ex);
            }
        }
        
        private VideoUrlInfo ExtractVideoUrlInfo(string url)
        {
            try
            {
                var urlInfo = new VideoUrlInfo();

                // 支持多种bilibili链接格式
                var patterns = new[]
                {
                    @"bilibili\.com/video/(BV[A-Za-z0-9]+)(?:\?p=(\d+))?",  // BV号带页面参数
                    @"bilibili\.com/video/av(\d+)(?:\?p=(\d+))?",           // AV号带页面参数
                    @"b23\.tv/([A-Za-z0-9]+)",                             // 短链接
                    @"bilibili\.com/video/([A-Za-z0-9]+)(?:\?p=(\d+))?",   // 通用格式
                    @"(?:bv|BV)([A-Za-z0-9]+)",                            // 直接BV号
                    @"(?:av|AV)(\d+)"                                      // 直接AV号
                };

                foreach (var pattern in patterns)
                {
                    var match = Regex.Match(url, pattern, RegexOptions.IgnoreCase);
                    if (match.Success)
                    {
                        var videoId = match.Groups[1].Value;

                        // 提取页面参数
                        if (match.Groups.Count > 2 && !string.IsNullOrEmpty(match.Groups[2].Value))
                        {
                            if (int.TryParse(match.Groups[2].Value, out int page))
                            {
                                urlInfo.Page = page;
                            }
                        }

                        // 如果是AV号，需要转换为BV号或直接使用
                        if (Regex.IsMatch(videoId, @"^\d+$"))
                        {
                            // 这是AV号
                            urlInfo.VideoId = "av" + videoId;
                        }
                        else
                        {
                            // 确保BV号格式正确
                            if (!videoId.StartsWith("BV", StringComparison.OrdinalIgnoreCase))
                            {
                                videoId = "BV" + videoId;
                            }
                            urlInfo.VideoId = videoId;
                        }

                        return urlInfo;
                    }
                }

                // 如果没有匹配到，尝试从URL参数中提取页面信息
                if (url.Contains("?p="))
                {
                    var pageMatch = Regex.Match(url, @"[?&]p=(\d+)");
                    if (pageMatch.Success && int.TryParse(pageMatch.Groups[1].Value, out int pageFromQuery))
                    {
                        urlInfo.Page = pageFromQuery;
                    }
                }

                return urlInfo;
            }
            catch (Exception ex)
            {
                throw new Exception($"解析视频URL信息失败: {ex.Message}", ex);
            }
        }

        private class VideoUrlInfo
        {
            public string VideoId { get; set; }
            public int Page { get; set; } = 1; // 默认第一页
        }
        
        private async Task<VideoInfo> GetVideoBasicInfoAsync(string videoId)
        {
            var apiUrl = $"https://api.bilibili.com/x/web-interface/view?bvid={videoId}";

            try
            {
                var response = await _httpClient.GetStringAsync(apiUrl);
                var jsonData = JsonConvert.DeserializeObject<dynamic>(response);

                if (jsonData.code != 0)
                {
                    throw new Exception($"API错误 (code: {jsonData.code}): {jsonData.message}");
                }

                var data = jsonData.data;
                var videoInfo = new VideoInfo
                {
                    VideoId = videoId,
                    Title = data.title?.ToString() ?? "未知标题",
                    Description = data.desc?.ToString() ?? "",
                    Author = data.owner?.name?.ToString() ?? "未知作者",
                    AuthorId = data.owner?.mid?.ToString() ?? "",
                    Duration = Convert.ToInt64(data.duration ?? 0),
                    CoverUrl = data.pic?.ToString() ?? "",
                    OriginalUrl = $"https://www.bilibili.com/video/{videoId}",
                    PublishTime = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(data.pubdate ?? 0)).DateTime
                };

                // 检查是否需要付费
                if (data.rights != null)
                {
                    videoInfo.IsPaymentRequired = Convert.ToBoolean(data.rights.pay ?? 0);
                    videoInfo.IsVip = Convert.ToBoolean(data.rights.hd5 ?? 0);
                }

                // 处理页面信息
                if (data.pages != null)
                {
                    var pagesArray = data.pages as Newtonsoft.Json.Linq.JArray;
                    if (pagesArray != null)
                    {
                        foreach (var pageItem in pagesArray)
                        {
                            var page = new VideoPage
                            {
                                Page = Convert.ToInt32(pageItem["page"] ?? 1),
                                Cid = Convert.ToInt64(pageItem["cid"] ?? 0),
                                Part = pageItem["part"]?.ToString() ?? $"第{pageItem["page"]}话",
                                Duration = Convert.ToInt64(pageItem["duration"] ?? 0),
                                Dimension = pageItem["dimension"]?.ToString() ?? ""
                            };
                            videoInfo.Pages.Add(page);
                        }

                        // 如果有多个页面，标记为合集
                        if (pagesArray.Count > 1)
                        {
                            videoInfo.IsCollection = true;
                        }
                    }
                }

                return videoInfo;
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"网络请求失败: {ex.Message}", ex);
            }
            catch (JsonException ex)
            {
                throw new Exception($"JSON解析失败: {ex.Message}", ex);
            }
        }
        
        public async Task<dynamic> GetVideoPlayInfoAsync(string videoId, int page = 1)
        {
            try
            {
                // 首先获取cid
                var cidUrl = $"https://api.bilibili.com/x/player/pagelist?bvid={videoId}";
                var cidResponse = await _httpClient.GetStringAsync(cidUrl);
                var cidData = JsonConvert.DeserializeObject<dynamic>(cidResponse);

                if (cidData.code != 0)
                {
                    throw new Exception($"获取CID失败 (code: {cidData.code}): {cidData.message}");
                }

                // 根据页面参数选择正确的cid
                var pages = cidData.data as Newtonsoft.Json.Linq.JArray;
                if (pages == null || pages.Count == 0)
                {
                    throw new Exception("未找到视频页面信息");
                }

                // 确保页面索引有效（页面从1开始，数组从0开始）
                var pageIndex = Math.Max(0, Math.Min(page - 1, pages.Count - 1));
                var cid = pages[pageIndex]["cid"];

                Logger.Instance.Info($"选择页面 {page}，CID: {cid}");

                // 获取播放信息 - 使用更新的参数
                var playUrl = $"https://api.bilibili.com/x/player/playurl?bvid={videoId}&cid={cid}&qn=120&fnval=4048&fourk=1&try_look=1";

                Logger.Instance.Info($"请求播放信息URL: {playUrl}");

                // 直接使用HttpClient，因为默认请求头已经设置好了
                var responseContent = await _httpClient.GetStringAsync(playUrl);
                var playData = JsonConvert.DeserializeObject<dynamic>(responseContent);

                Logger.Instance.Info($"播放信息API响应: code={playData?.code}, message={playData?.message}");

                return playData;
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"获取播放信息失败: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                throw new Exception($"解析播放信息失败: {ex.Message}", ex);
            }
        }
        
        private void MergeVideoInfo(VideoInfo videoInfo, dynamic playInfo)
        {
            Logger.Instance.Info($"开始合并视频播放信息，API响应码: {playInfo.code}");

            if (playInfo.code != 0)
            {
                Logger.Instance.Warning($"获取播放信息失败，错误码: {playInfo.code}, 消息: {playInfo.message}");

                // 如果获取播放信息失败，可能是权限问题
                if (playInfo.code == -10403)
                {
                    videoInfo.IsPaymentRequired = true;
                    Logger.Instance.Info("检测到付费视频");
                }
                return;
            }

            var data = playInfo.data;
            Logger.Instance.Info("播放信息数据获取成功，开始解析清晰度");

            // 解析可用的清晰度
            if (data.dash != null)
            {
                Logger.Instance.Info("检测到DASH格式，开始解析DASH清晰度");
                // DASH格式（音视频分离）
                ParseDashQualities(videoInfo, data.dash);
            }
            else if (data.durl != null)
            {
                Logger.Instance.Info("检测到传统格式，开始解析传统清晰度");
                // 传统格式（音视频合并）
                ParseTraditionalQualities(videoInfo, data.durl);
            }
            else
            {
                Logger.Instance.Warning("未找到DASH或DURL数据，无法解析清晰度");
            }

            Logger.Instance.Info($"清晰度解析完成，共找到 {videoInfo.AvailableQualities.Count} 个清晰度选项");
        }
        
        private void ParseDashQualities(VideoInfo videoInfo, dynamic dash)
        {
            Logger.Instance.Info("开始解析DASH清晰度信息");

            if (dash.video != null)
            {
                var videoArray = dash.video as Newtonsoft.Json.Linq.JArray;
                Logger.Instance.Info($"找到 {videoArray?.Count ?? 0} 个视频流");

                foreach (var video in dash.video)
                {
                    try
                    {
                        var qualityId = Convert.ToInt32(video.id);
                        var qualityName = GetQualityName(qualityId);

                        Logger.Instance.Info($"解析视频流: ID={qualityId}, 名称={qualityName}");

                        var quality = new VideoQuality
                        {
                            Quality = qualityId,
                            QualityName = qualityName,
                            VideoUrl = video.baseUrl?.ToString(),
                            FileSize = Convert.ToInt64(video.bandwidth ?? 0),
                            Format = "mp4",
                            Codec = video.codecs?.ToString()
                        };

                        // 查找对应的音频流
                        if (dash.audio != null && dash.audio.Count > 0)
                        {
                            quality.AudioUrl = dash.audio[0].baseUrl?.ToString();
                            Logger.Instance.Info($"为清晰度 {qualityName} 找到音频流");
                        }
                        else
                        {
                            Logger.Instance.Warning($"清晰度 {qualityName} 没有找到音频流");
                        }

                        videoInfo.AvailableQualities.Add(quality);
                        Logger.Instance.Info($"成功添加清晰度选项: {qualityName}");
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"解析视频流时出错: {ex.Message}", ex);
                    }
                }
            }
            else
            {
                Logger.Instance.Warning("DASH数据中没有找到video字段");
            }
        }
        
        private void ParseTraditionalQualities(VideoInfo videoInfo, dynamic durl)
        {
            Logger.Instance.Info("开始解析传统格式清晰度信息");

            var durlArray = durl as Newtonsoft.Json.Linq.JArray;
            Logger.Instance.Info($"找到 {durlArray?.Count ?? 0} 个传统格式流");

            foreach (var item in durl)
            {
                try
                {
                    var qualityId = Convert.ToInt32(item.quality ?? 80);
                    var qualityName = GetQualityName(qualityId);

                    Logger.Instance.Info($"解析传统流: ID={qualityId}, 名称={qualityName}");

                    var quality = new VideoQuality
                    {
                        Quality = qualityId,
                        QualityName = qualityName,
                        VideoUrl = item.url?.ToString(),
                        FileSize = Convert.ToInt64(item.size ?? 0),
                        Format = "flv"
                    };

                    videoInfo.AvailableQualities.Add(quality);
                    Logger.Instance.Info($"成功添加传统格式清晰度选项: {qualityName}");
                }
                catch (Exception ex)
                {
                    Logger.Instance.Error($"解析传统格式流时出错: {ex.Message}", ex);
                }
            }
        }
        
        private string GetQualityName(int quality)
        {
            return quality switch
            {
                120 => "4K",
                116 => "1080P60",
                112 => "1080P+",
                80 => "1080P",
                74 => "720P60",
                64 => "720P",
                32 => "480P",
                16 => "360P",
                _ => $"质量{quality}"
            };
        }
        
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
