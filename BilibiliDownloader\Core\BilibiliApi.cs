using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using BilibiliDownloader.Models;

namespace BilibiliDownloader.Core
{
    public class BilibiliApi
    {
        private readonly HttpClient _httpClient;
        private string _currentCookie;
        
        public BilibiliApi()
        {
            _httpClient = new HttpClient();
            SetupHttpClient();
        }
        
        private void SetupHttpClient()
        {
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            _httpClient.DefaultRequestHeaders.Add("Referer", "https://www.bilibili.com/");
        }
        
        public void SetCookie(string cookie)
        {
            _currentCookie = cookie;
            _httpClient.DefaultRequestHeaders.Remove("Cookie");
            if (!string.IsNullOrEmpty(cookie))
            {
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
            }
        }
        
        public async Task<VideoInfo> GetVideoInfoAsync(string url)
        {
            try
            {
                var videoId = ExtractVideoId(url);
                if (string.IsNullOrEmpty(videoId))
                {
                    throw new ArgumentException("无效的视频链接");
                }
                
                // 获取视频基本信息
                var videoInfo = await GetVideoBasicInfoAsync(videoId);
                
                // 获取视频播放信息
                var playInfo = await GetVideoPlayInfoAsync(videoId);
                
                // 合并信息
                MergeVideoInfo(videoInfo, playInfo);
                
                return videoInfo;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取视频信息失败: {ex.Message}", ex);
            }
        }
        
        private string ExtractVideoId(string url)
        {
            // 支持多种bilibili链接格式
            var patterns = new[]
            {
                @"bilibili\.com/video/([A-Za-z0-9]+)",
                @"bilibili\.com/video/av(\d+)",
                @"b23\.tv/([A-Za-z0-9]+)"
            };
            
            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }
            
            return null;
        }
        
        private async Task<VideoInfo> GetVideoBasicInfoAsync(string videoId)
        {
            var apiUrl = $"https://api.bilibili.com/x/web-interface/view?bvid={videoId}";
            
            var response = await _httpClient.GetStringAsync(apiUrl);
            var jsonData = JsonConvert.DeserializeObject<dynamic>(response);
            
            if (jsonData.code != 0)
            {
                throw new Exception($"API错误: {jsonData.message}");
            }
            
            var data = jsonData.data;
            var videoInfo = new VideoInfo
            {
                VideoId = videoId,
                Title = data.title,
                Description = data.desc,
                Author = data.owner.name,
                AuthorId = data.owner.mid.ToString(),
                Duration = data.duration,
                CoverUrl = data.pic,
                OriginalUrl = $"https://www.bilibili.com/video/{videoId}",
                PublishTime = DateTimeOffset.FromUnixTimeSeconds((long)data.pubdate).DateTime
            };
            
            // 检查是否需要付费
            if (data.rights != null)
            {
                videoInfo.IsPaymentRequired = data.rights.pay == 1;
                videoInfo.IsVip = data.rights.hd5 == 1;
            }
            
            // 检查是否是合集
            if (data.pages != null && data.pages.Count > 1)
            {
                videoInfo.IsCollection = true;
                // 这里可以进一步处理合集信息
            }
            
            return videoInfo;
        }
        
        private async Task<dynamic> GetVideoPlayInfoAsync(string videoId)
        {
            // 首先获取cid
            var cidUrl = $"https://api.bilibili.com/x/player/pagelist?bvid={videoId}";
            var cidResponse = await _httpClient.GetStringAsync(cidUrl);
            var cidData = JsonConvert.DeserializeObject<dynamic>(cidResponse);
            
            if (cidData.code != 0)
            {
                throw new Exception($"获取CID失败: {cidData.message}");
            }
            
            var cid = cidData.data[0].cid;
            
            // 获取播放信息
            var playUrl = $"https://api.bilibili.com/x/player/playurl?bvid={videoId}&cid={cid}&qn=80&fnval=4048&fourk=1";
            var playResponse = await _httpClient.GetStringAsync(playUrl);
            var playData = JsonConvert.DeserializeObject<dynamic>(playResponse);
            
            return playData;
        }
        
        private void MergeVideoInfo(VideoInfo videoInfo, dynamic playInfo)
        {
            if (playInfo.code != 0)
            {
                // 如果获取播放信息失败，可能是权限问题
                if (playInfo.code == -10403)
                {
                    videoInfo.IsPaymentRequired = true;
                }
                return;
            }
            
            var data = playInfo.data;
            
            // 解析可用的清晰度
            if (data.dash != null)
            {
                // DASH格式（音视频分离）
                ParseDashQualities(videoInfo, data.dash);
            }
            else if (data.durl != null)
            {
                // 传统格式（音视频合并）
                ParseTraditionalQualities(videoInfo, data.durl);
            }
        }
        
        private void ParseDashQualities(VideoInfo videoInfo, dynamic dash)
        {
            if (dash.video != null)
            {
                foreach (var video in dash.video)
                {
                    var quality = new VideoQuality
                    {
                        Quality = video.id,
                        QualityName = GetQualityName(video.id),
                        VideoUrl = video.baseUrl,
                        FileSize = video.bandwidth,
                        Format = "mp4",
                        Codec = video.codecs
                    };
                    
                    // 查找对应的音频流
                    if (dash.audio != null && dash.audio.Count > 0)
                    {
                        quality.AudioUrl = dash.audio[0].baseUrl;
                    }
                    
                    videoInfo.AvailableQualities.Add(quality);
                }
            }
        }
        
        private void ParseTraditionalQualities(VideoInfo videoInfo, dynamic durl)
        {
            foreach (var item in durl)
            {
                var quality = new VideoQuality
                {
                    Quality = item.quality ?? 80,
                    QualityName = GetQualityName(item.quality ?? 80),
                    VideoUrl = item.url,
                    FileSize = item.size,
                    Format = "flv"
                };
                
                videoInfo.AvailableQualities.Add(quality);
            }
        }
        
        private string GetQualityName(int quality)
        {
            return quality switch
            {
                120 => "4K",
                116 => "1080P60",
                112 => "1080P+",
                80 => "1080P",
                74 => "720P60",
                64 => "720P",
                32 => "480P",
                16 => "360P",
                _ => $"质量{quality}"
            };
        }
        
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
