# Bilibili下载器 - 安全测试工具

这是一个用于测试bilibili网络安全的专业工具，仅供安全研究和测试使用。

## 功能特性

### ✅ 已实现功能

1. **完整的账号管理系统**
   - ✅ 支持用户名/密码登录（带验证）
   - ✅ 支持Cookie登录（推荐，已验证）
   - ✅ 账号信息本地存储（明文存储，仅用于测试）
   - ✅ 多账号管理和快速切换
   - ✅ 登录状态验证和用户信息获取

2. **强化的视频解析功能**
   - ✅ 支持多种bilibili链接格式（BV号、AV号、短链接）
   - ✅ 自动解析视频基本信息（标题、作者、时长等）
   - ✅ 检测付费状态和会员要求
   - ✅ 获取可用清晰度列表（DASH和传统格式）
   - ✅ **完美支持合集视频页面选择**（解决了合集视频下载问题）
   - ✅ 智能识别URL中的页面参数（?p=数字）
   - ✅ 改进的错误处理和重试机制

3. **专业的下载管理**
   - ✅ 多线程下载支持
   - ✅ 实时进度显示和速度监控
   - ✅ 下载队列管理
   - ✅ 音视频分离下载支持
   - ✅ 下载历史记录

4. **安全测试功能**
   - ✅ 付费绕过检测选项
   - ✅ 完整的日志记录系统（文件+界面）
   - ✅ 详细的错误信息收集和分析
   - ✅ API调用监控和调试信息

5. **合集视频支持**
   - ✅ **自动识别合集视频**（多P视频）
   - ✅ **智能页面参数解析**（支持?p=数字格式）
   - ✅ **合集视频列表显示**（可展开查看所有视频）
   - ✅ **一键切换合集中的视频**（点击即可切换）
   - ✅ **当前页面信息显示**（显示P几和标题）

6. **系统优化**
   - ✅ 改进的API接口调用（2024年最新规范）
   - ✅ 更好的异常处理和用户反馈
   - ✅ 自动日志清理和管理
   - ✅ 内存和资源管理优化

### 🚧 计划中功能

1. **高级绕过技术**
   - Cookie重放攻击测试
   - API参数篡改测试
   - 时间戳操作测试
   - 移动端API差异利用

2. **合集下载**
   - 批量下载整个视频合集
   - 自动解析合集中的所有视频

3. **音视频处理**
   - FFmpeg集成用于音视频合并
   - 多种格式输出支持

## 使用说明

### 1. 启动程序
```bash
cd BilibiliDownloader
dotnet run
```

### 2. 配置账号（重要！）
1. 点击"账号管理"按钮
2. 添加bilibili账号：
   - **方式一**：输入用户名和密码，点击"测试登录"验证
   - **方式二**：输入Cookie（推荐），点击"测试登录"验证
3. 验证成功后点击"添加账号"保存
4. 选择要使用的账号并点击"使用此账号"

### 3. 下载视频
1. 在主界面输入bilibili视频链接（支持BV号、AV号、短链接）
   - 支持合集视频链接，如：`https://www.bilibili.com/video/BV1234567890?p=2`
2. 点击"解析视频"获取视频信息
3. 查看右侧的视频信息面板确认解析结果
4. **合集视频特殊功能**：
   - 如果是合集视频，会显示"合集视频列表"可展开区域
   - 点击列表中的任意视频可快速切换到该视频
   - 当前选择的视频会在"当前页面"中显示
5. 选择下载清晰度（1080P、720P等）
6. 可选功能：
   - 勾选"下载整个合集"（如果是合集视频）
   - 勾选"尝试绕过付费检测"进行安全测试
7. 点击"开始下载"

### 4. 获取Cookie的方法（推荐）
1. 在浏览器中登录bilibili
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面或访问任意bilibili页面
5. 找到任意请求，在Request Headers中找到Cookie
6. 复制完整的Cookie字符串
7. 将Cookie粘贴到程序的"Cookie登录"框中

### 5. 故障排除
- **解析失败**：检查网络连接，确保已登录有效账号
- **下载失败**：检查视频是否需要付费或会员权限
- **登录失败**：确认用户名密码正确，或使用Cookie登录
- **查看日志**：程序右下角有详细的日志输出，可以查看具体错误信息

## 项目结构

```
BilibiliDownloader/
├── Core/                    # 核心功能
│   ├── BilibiliApi.cs      # bilibili API接口
│   └── DownloadManager.cs  # 下载管理器
├── Models/                  # 数据模型
│   ├── AccountInfo.cs      # 账号信息
│   ├── VideoInfo.cs        # 视频信息
│   └── DownloadItem.cs     # 下载项
├── UI/                      # 用户界面
│   └── AccountManagerWindow.xaml  # 账号管理窗口
├── Utils/                   # 工具类
│   └── AccountManager.cs   # 账号管理器
└── MainWindow.xaml         # 主窗口
```

## 安全测试功能

### 付费绕过测试
程序提供了"尝试绕过付费检测"选项，用于测试以下安全漏洞：

1. **权限验证绕过**
   - 测试服务端权限验证的严格程度
   - 检查是否存在权限提升漏洞

2. **Cookie重放攻击**
   - 测试已付费用户Cookie的重用可能性
   - 验证会话管理的安全性

3. **API参数篡改**
   - 测试通过修改请求参数绕过验证
   - 检查参数验证的完整性

## 注意事项

⚠️ **重要提醒**：
- 本工具仅用于安全研究和测试目的
- 请遵守相关法律法规和服务条款
- 不得用于商业用途或侵犯版权
- 账号密码以明文形式存储，请注意安全

## 技术栈

- **.NET 6.0** - 主要开发框架
- **WPF** - 用户界面框架
- **Newtonsoft.Json** - JSON处理
- **HttpClient** - HTTP请求处理

## 开发计划

1. **Phase 1** ✅ - 基础功能实现
2. **Phase 2** 🚧 - 高级安全测试功能
3. **Phase 3** 📋 - FFmpeg集成和音视频处理
4. **Phase 4** 📋 - 自动化测试套件

## 贡献指南

欢迎提交Issue和Pull Request来改进这个工具。

## 许可证

本项目仅用于教育和安全研究目的。
