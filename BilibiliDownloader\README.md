# Bilibili下载器 - 安全测试工具

这是一个用于测试bilibili网络安全的专业工具，仅供安全研究和测试使用。

## 功能特性

### ✅ 已实现功能

1. **账号管理系统**
   - 支持用户名/密码登录
   - 支持Cookie登录（推荐）
   - 账号信息本地存储（明文存储，仅用于测试）
   - 多账号管理和切换

2. **视频解析功能**
   - 支持多种bilibili链接格式
   - 自动解析视频基本信息
   - 检测付费状态和会员要求
   - 获取可用清晰度列表

3. **下载管理**
   - 多线程下载支持
   - 实时进度显示
   - 下载速度监控
   - 下载历史记录

4. **安全测试功能**
   - 付费绕过检测选项
   - 详细的日志记录
   - 错误信息收集

### 🚧 计划中功能

1. **高级绕过技术**
   - Cookie重放攻击测试
   - API参数篡改测试
   - 时间戳操作测试
   - 移动端API差异利用

2. **合集下载**
   - 批量下载整个视频合集
   - 自动解析合集中的所有视频

3. **音视频处理**
   - FFmpeg集成用于音视频合并
   - 多种格式输出支持

## 使用说明

### 1. 启动程序
```bash
cd BilibiliDownloader
dotnet run
```

### 2. 配置账号
1. 点击"账号管理"按钮
2. 添加bilibili账号：
   - **方式一**：输入用户名和密码
   - **方式二**：输入Cookie（推荐）
3. 选择要使用的账号

### 3. 下载视频
1. 在主界面输入bilibili视频链接
2. 点击"解析视频"获取视频信息
3. 选择下载清晰度
4. 可选：勾选"尝试绕过付费检测"进行安全测试
5. 点击"开始下载"

### 4. 获取Cookie的方法
1. 在浏览器中登录bilibili
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面
5. 找到任意请求，复制Cookie头部信息
6. 将Cookie粘贴到程序中

## 项目结构

```
BilibiliDownloader/
├── Core/                    # 核心功能
│   ├── BilibiliApi.cs      # bilibili API接口
│   └── DownloadManager.cs  # 下载管理器
├── Models/                  # 数据模型
│   ├── AccountInfo.cs      # 账号信息
│   ├── VideoInfo.cs        # 视频信息
│   └── DownloadItem.cs     # 下载项
├── UI/                      # 用户界面
│   └── AccountManagerWindow.xaml  # 账号管理窗口
├── Utils/                   # 工具类
│   └── AccountManager.cs   # 账号管理器
└── MainWindow.xaml         # 主窗口
```

## 安全测试功能

### 付费绕过测试
程序提供了"尝试绕过付费检测"选项，用于测试以下安全漏洞：

1. **权限验证绕过**
   - 测试服务端权限验证的严格程度
   - 检查是否存在权限提升漏洞

2. **Cookie重放攻击**
   - 测试已付费用户Cookie的重用可能性
   - 验证会话管理的安全性

3. **API参数篡改**
   - 测试通过修改请求参数绕过验证
   - 检查参数验证的完整性

## 注意事项

⚠️ **重要提醒**：
- 本工具仅用于安全研究和测试目的
- 请遵守相关法律法规和服务条款
- 不得用于商业用途或侵犯版权
- 账号密码以明文形式存储，请注意安全

## 技术栈

- **.NET 6.0** - 主要开发框架
- **WPF** - 用户界面框架
- **Newtonsoft.Json** - JSON处理
- **HttpClient** - HTTP请求处理

## 开发计划

1. **Phase 1** ✅ - 基础功能实现
2. **Phase 2** 🚧 - 高级安全测试功能
3. **Phase 3** 📋 - FFmpeg集成和音视频处理
4. **Phase 4** 📋 - 自动化测试套件

## 贡献指南

欢迎提交Issue和Pull Request来改进这个工具。

## 许可证

本项目仅用于教育和安全研究目的。
