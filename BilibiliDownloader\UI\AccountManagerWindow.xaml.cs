using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using BilibiliDownloader.Core;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.UI
{
    public partial class AccountManagerWindow : Window
    {
        private AccountManager _accountManager;
        private AccountInfo _selectedAccount;
        private BilibiliLoginService _loginService;

        public AccountInfo SelectedAccount { get; private set; }

        public AccountManagerWindow(AccountManager accountManager)
        {
            InitializeComponent();
            _accountManager = accountManager;
            _loginService = new BilibiliLoginService();
            LoadAccounts();
        }
        
        private void LoadAccounts()
        {
            lbAccounts.ItemsSource = _accountManager.GetAllAccounts();
            
            // 选中当前活跃账号
            var currentAccount = _accountManager.GetCurrentAccount();
            if (currentAccount != null)
            {
                lbAccounts.SelectedItem = currentAccount;
            }
        }
        
        private void LbAccounts_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedAccount = lbAccounts.SelectedItem as AccountInfo;
            btnDeleteAccount.IsEnabled = _selectedAccount != null;
            btnUseAccount.IsEnabled = _selectedAccount != null;
            
            if (_selectedAccount != null)
            {
                txtUsername.Text = _selectedAccount.Username;
                // 出于安全考虑，不显示密码
                txtPassword.Password = "";
                txtCookie.Text = _selectedAccount.Cookie ?? "";
            }
        }
        
        private void BtnAddAccount_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtPassword.Password))
            {
                MessageBox.Show("请输入密码", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            var account = new AccountInfo(txtUsername.Text.Trim(), txtPassword.Password);
            _accountManager.AddAccount(account);
            
            LoadAccounts();
            ClearInputs();
            
            MessageBox.Show("账号添加成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void BtnAddCookie_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtCookie.Text))
            {
                MessageBox.Show("请输入Cookie", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            var account = AccountInfo.CreateWithCookie(txtUsername.Text.Trim(), txtCookie.Text.Trim());
            _accountManager.AddAccount(account);
            
            LoadAccounts();
            ClearInputs();
            
            MessageBox.Show("Cookie账号添加成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private async void BtnTestLogin_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            btnTestLogin.IsEnabled = false;
            btnTestLogin.Content = "测试中...";

            try
            {
                LoginResult result;

                if (!string.IsNullOrWhiteSpace(txtCookie.Text))
                {
                    // 测试Cookie登录
                    result = await _loginService.ValidateCookieAsync(txtCookie.Text);
                }
                else if (!string.IsNullOrWhiteSpace(txtPassword.Password))
                {
                    // 测试用户名密码登录
                    result = await _loginService.LoginWithPasswordAsync(txtUsername.Text, txtPassword.Password);
                }
                else
                {
                    MessageBox.Show("请输入密码或Cookie", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (result.Success)
                {
                    var message = $"登录测试成功！\n{result.Message}";
                    if (result.UserInfo != null)
                    {
                        message += $"\n用户ID: {result.UserInfo.UserId}";
                        message += $"\n用户名: {result.UserInfo.Username}";
                        message += $"\n会员状态: {(result.UserInfo.IsVip ? "大会员" : "普通用户")}";
                    }
                    MessageBox.Show(message, "登录成功", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 如果Cookie为空，自动填入获取到的Cookie
                    if (string.IsNullOrWhiteSpace(txtCookie.Text) && !string.IsNullOrWhiteSpace(result.Cookie))
                    {
                        txtCookie.Text = result.Cookie;
                    }
                }
                else
                {
                    MessageBox.Show($"登录测试失败：{result.Message}", "登录失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"登录测试出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnTestLogin.IsEnabled = true;
                btnTestLogin.Content = "测试登录";
            }
        }
        
        private void BtnDeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;
            
            var result = MessageBox.Show($"确定要删除账号 '{_selectedAccount.Username}' 吗？", 
                                       "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _accountManager.RemoveAccount(_selectedAccount);
                LoadAccounts();
                ClearInputs();
            }
        }
        
        private void BtnUseAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;
            
            _accountManager.SetCurrentAccount(_selectedAccount);
            SelectedAccount = _selectedAccount;
            
            MessageBox.Show($"已切换到账号: {_selectedAccount.Username}", "提示", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void BtnOpenAccountFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = _accountManager.GetAccountFilePath();
                Process.Start(new ProcessStartInfo
                {
                    FileName = "notepad.exe",
                    Arguments = filePath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开账号文件: {ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _loginService?.Dispose();
            base.OnClosed(e);
        }
        
        private void ClearInputs()
        {
            txtUsername.Text = "";
            txtPassword.Password = "";
            txtCookie.Text = "";
        }
    }
}
