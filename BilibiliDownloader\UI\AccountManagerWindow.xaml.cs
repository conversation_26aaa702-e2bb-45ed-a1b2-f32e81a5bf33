using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using BilibiliDownloader.Core;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.UI
{
    public partial class AccountManagerWindow : Window
    {
        private readonly AccountManager _accountManager;
        private readonly PlatformManager _platformManager;
        private AccountInfo? _selectedAccount;
        private readonly List<PlatformInfo> _platforms;

        public AccountInfo? SelectedAccount { get; private set; }

        public class PlatformInfo
        {
            public string Name { get; set; } = "";
            public string Icon { get; set; } = "";
            public bool RequiresLogin { get; set; }
        }

        public AccountManagerWindow(AccountManager accountManager)
        {
            try
            {
                InitializeComponent();

                _accountManager = accountManager ?? throw new ArgumentNullException(nameof(accountManager));
                _platformManager = PlatformManager.Instance ?? throw new InvalidOperationException("PlatformManager未初始化");
                _platforms = new List<PlatformInfo>();

                // 延迟初始化数据和事件处理
                Loaded += AccountManagerWindow_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"账号管理窗口初始化失败：{ex.Message}", "初始化错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void AccountManagerWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 设置UI事件处理
                if (rbPassword != null && rbCookie != null)
                {
                    rbPassword.Checked += (s, e) => ToggleLoginMethod(true);
                    rbCookie.Checked += (s, e) => ToggleLoginMethod(false);
                }

                InitializeData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"账号管理窗口加载失败：{ex.Message}", "加载错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeData()
        {
            try
            {
                InitializePlatforms();
                LoadAccounts();
                UpdateAccountStats();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ToggleLoginMethod(bool isPasswordMode)
        {
            if (pnlPassword != null)
                pnlPassword.Visibility = isPasswordMode ? Visibility.Visible : Visibility.Collapsed;
            if (pnlCookie != null)
                pnlCookie.Visibility = isPasswordMode ? Visibility.Collapsed : Visibility.Visible;
        }

        private void InitializePlatforms()
        {
            _platforms.Clear();

            var allPlatforms = _platformManager.GetAllPlatforms();
            foreach (var platform in allPlatforms)
            {
                _platforms.Add(new PlatformInfo
                {
                    Name = platform.PlatformName,
                    Icon = platform.PlatformIcon,
                    RequiresLogin = platform.RequiresLogin
                });
            }

            // 设置下拉框数据源
            if (cmbPlatform != null)
            {
                cmbPlatform.ItemsSource = _platforms;
                if (_platforms.Count > 0)
                    cmbPlatform.SelectedIndex = 0;
            }

            // 设置筛选下拉框
            if (cmbPlatformFilter != null)
            {
                var filterItems = new List<string> { "全部平台" };
                filterItems.AddRange(_platforms.Select(p => $"{p.Icon} {p.Name}"));
                cmbPlatformFilter.ItemsSource = filterItems;
                cmbPlatformFilter.SelectedIndex = 0;
            }
        }

        private void LoadAccounts()
        {
            var allAccounts = _accountManager.GetAllAccounts();

            // 根据筛选条件过滤账号
            if (cmbPlatformFilter != null && cmbPlatformFilter.SelectedIndex > 0 && _platforms.Count > 0)
            {
                var filterIndex = cmbPlatformFilter.SelectedIndex - 1;
                if (filterIndex >= 0 && filterIndex < _platforms.Count)
                {
                    var selectedPlatform = _platforms[filterIndex].Name;
                    allAccounts = allAccounts.Where(a => a.Platform == selectedPlatform).ToList();
                }
            }

            if (lbAccounts != null)
            {
                lbAccounts.ItemsSource = allAccounts;

                // 选中当前活跃账号
                var currentAccount = _accountManager.GetCurrentAccount();
                if (currentAccount != null)
                {
                    lbAccounts.SelectedItem = currentAccount;
                }
            }
        }

        private void UpdateAccountStats()
        {
            if (txtAccountStats == null) return;

            var allAccounts = _accountManager.GetAllAccounts();
            var totalAccounts = allAccounts.Count;
            var validAccounts = allAccounts.Count(a => a.IsValid);
            var platformCount = allAccounts.Select(a => a.Platform).Distinct().Count();

            txtAccountStats.Text = $"📊 总计: {totalAccounts}个账号 | 有效: {validAccounts}个 | 平台: {platformCount}个";
        }
        
        private void LbAccounts_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedAccount = lbAccounts.SelectedItem as AccountInfo;
            var hasSelection = _selectedAccount != null;
            
            btnDeleteAccount.IsEnabled = hasSelection;
            btnUseAccount.IsEnabled = hasSelection;
            btnValidateAccount.IsEnabled = hasSelection;
            
            if (hasSelection)
            {
                // 填充表单
                var platformInfo = _platforms.FirstOrDefault(p => p.Name == _selectedAccount.Platform);
                if (platformInfo != null)
                    cmbPlatform.SelectedItem = platformInfo;
                
                txtUsername.Text = _selectedAccount.Username;
                txtNickname.Text = _selectedAccount.Nickname ?? "";
                txtPassword.Password = "";
                txtCookie.Text = _selectedAccount.Cookie ?? "";
                
                // 根据账号类型选择登录方式
                if (!string.IsNullOrEmpty(_selectedAccount.Cookie))
                {
                    rbCookie.IsChecked = true;
                }
                else
                {
                    rbPassword.IsChecked = true;
                }
            }
        }

        private void CmbPlatformFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            LoadAccounts();
        }

        private void CmbPlatform_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 平台选择变更处理
        }
        
        private void BtnAddAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedPlatform = cmbPlatform.SelectedItem as PlatformInfo;
                if (selectedPlatform == null)
                {
                    MessageBox.Show("请选择平台", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                AccountInfo account;
                
                if (rbCookie.IsChecked == true)
                {
                    // Cookie登录
                    if (string.IsNullOrWhiteSpace(txtCookie.Text))
                    {
                        MessageBox.Show("请输入Cookie", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                    
                    account = AccountInfo.CreateWithCookie(selectedPlatform.Name, selectedPlatform.Icon, 
                                                         txtUsername.Text.Trim(), txtCookie.Text.Trim());
                }
                else
                {
                    // 密码登录
                    if (string.IsNullOrWhiteSpace(txtPassword.Password))
                    {
                        MessageBox.Show("请输入密码", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                    
                    account = new AccountInfo(selectedPlatform.Name, selectedPlatform.Icon, txtUsername.Text.Trim())
                    {
                        Password = txtPassword.Password
                    };
                }

                account.Nickname = txtNickname.Text.Trim();
                
                _accountManager.AddAccount(account);
                LoadAccounts();
                UpdateAccountStats();
                ClearInputs();
                
                MessageBox.Show($"✅ {selectedPlatform.Icon} {selectedPlatform.Name} 账号添加成功！", "添加成功", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加账号失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnTestLogin_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("测试登录功能开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnDeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;

            var result = MessageBox.Show($"确定要删除账号 '{_selectedAccount.DisplayName}' 吗？", 
                                       "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _accountManager.RemoveAccount(_selectedAccount);
                LoadAccounts();
                UpdateAccountStats();
                ClearInputs();
            }
        }
        
        private void BtnUseAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;
            
            _accountManager.SetCurrentAccount(_selectedAccount.Platform, _selectedAccount);
            SelectedAccount = _selectedAccount;
            
            MessageBox.Show($"已切换到账号: {_selectedAccount.DisplayName}", "切换成功", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void BtnValidateAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;

            btnValidateAccount.IsEnabled = false;
            btnValidateAccount.Content = "🔄 验证中...";

            try
            {
                var isValid = await _accountManager.ValidateAccountAsync(_selectedAccount);
                var status = isValid ? "✅ 有效" : "❌ 无效";
                var message = isValid ? 
                    $"账号验证成功！\n\n{_selectedAccount.PlatformDisplayName}\n用户: {_selectedAccount.DisplayName}" :
                    $"账号验证失败！\n\n{_selectedAccount.PlatformDisplayName}\n用户: {_selectedAccount.DisplayName}\n\n请检查账号信息是否正确。";

                MessageBox.Show(message, $"账号验证结果 - {status}", 
                              MessageBoxButton.OK, 
                              isValid ? MessageBoxImage.Information : MessageBoxImage.Warning);

                LoadAccounts(); // 刷新列表以更新验证状态
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证过程中发生错误：\n{ex.Message}", "验证错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnValidateAccount.IsEnabled = true;
                btnValidateAccount.Content = "🔍 验证";
            }
        }

        private void BtnRefreshAccounts_Click(object sender, RoutedEventArgs e)
        {
            LoadAccounts();
            UpdateAccountStats();
            MessageBox.Show("账号列表已刷新", "刷新完成", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnOpenAccountFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = _accountManager.GetAccountFilePath();
                Process.Start(new ProcessStartInfo
                {
                    FileName = "notepad.exe",
                    Arguments = filePath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开账号文件: {ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void ClearInputs()
        {
            txtUsername.Text = "";
            txtNickname.Text = "";
            txtPassword.Password = "";
            txtCookie.Text = "";
            if (_platforms.Count > 0)
                cmbPlatform.SelectedIndex = 0;
            rbPassword.IsChecked = true;
        }
    }
}
