using System;
using System.Diagnostics;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.UI
{
    public partial class AccountManagerWindow : Window
    {
        private AccountManager _accountManager;
        private AccountInfo _selectedAccount;
        
        public AccountInfo SelectedAccount { get; private set; }
        
        public AccountManagerWindow(AccountManager accountManager)
        {
            InitializeComponent();
            _accountManager = accountManager;
            LoadAccounts();
        }
        
        private void LoadAccounts()
        {
            lbAccounts.ItemsSource = _accountManager.GetAllAccounts();
            
            // 选中当前活跃账号
            var currentAccount = _accountManager.GetCurrentAccount();
            if (currentAccount != null)
            {
                lbAccounts.SelectedItem = currentAccount;
            }
        }
        
        private void LbAccounts_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedAccount = lbAccounts.SelectedItem as AccountInfo;
            btnDeleteAccount.IsEnabled = _selectedAccount != null;
            btnUseAccount.IsEnabled = _selectedAccount != null;
            
            if (_selectedAccount != null)
            {
                txtUsername.Text = _selectedAccount.Username;
                // 出于安全考虑，不显示密码
                txtPassword.Password = "";
                txtCookie.Text = _selectedAccount.Cookie ?? "";
            }
        }
        
        private void BtnAddAccount_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtPassword.Password))
            {
                MessageBox.Show("请输入密码", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            var account = new AccountInfo(txtUsername.Text.Trim(), txtPassword.Password);
            _accountManager.AddAccount(account);
            
            LoadAccounts();
            ClearInputs();
            
            MessageBox.Show("账号添加成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void BtnAddCookie_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (string.IsNullOrWhiteSpace(txtCookie.Text))
            {
                MessageBox.Show("请输入Cookie", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            var account = AccountInfo.CreateWithCookie(txtUsername.Text.Trim(), txtCookie.Text.Trim());
            _accountManager.AddAccount(account);
            
            LoadAccounts();
            ClearInputs();
            
            MessageBox.Show("Cookie账号添加成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void BtnTestLogin_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("登录测试功能将在后续版本中实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void BtnDeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;
            
            var result = MessageBox.Show($"确定要删除账号 '{_selectedAccount.Username}' 吗？", 
                                       "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _accountManager.RemoveAccount(_selectedAccount);
                LoadAccounts();
                ClearInputs();
            }
        }
        
        private void BtnUseAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;
            
            _accountManager.SetCurrentAccount(_selectedAccount);
            SelectedAccount = _selectedAccount;
            
            MessageBox.Show($"已切换到账号: {_selectedAccount.Username}", "提示", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void BtnOpenAccountFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = _accountManager.GetAccountFilePath();
                Process.Start(new ProcessStartInfo
                {
                    FileName = "notepad.exe",
                    Arguments = filePath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开账号文件: {ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
        
        private void ClearInputs()
        {
            txtUsername.Text = "";
            txtPassword.Password = "";
            txtCookie.Text = "";
        }
    }
}
