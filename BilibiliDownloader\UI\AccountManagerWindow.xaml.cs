using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using BilibiliDownloader.Core;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.UI
{
    public partial class AccountManagerWindow : Window
    {
        private AccountManager _accountManager;
        private PlatformManager _platformManager;
        private AccountInfo? _selectedAccount;
        private BilibiliLoginService _loginService;
        private List<PlatformInfo> _platforms;

        public AccountInfo? SelectedAccount { get; private set; }

        public AccountManagerWindow(AccountManager accountManager)
        {
            InitializeComponent();
            _accountManager = accountManager;
            _platformManager = PlatformManager.Instance;
            _loginService = new BilibiliLoginService();
            _platforms = new List<PlatformInfo>();

            InitializePlatforms();
            LoadAccounts();
            UpdateAccountStats();
        }

        /// <summary>
        /// 平台信息类
        /// </summary>
        public class PlatformInfo
        {
            public string Name { get; set; } = "";
            public string Icon { get; set; } = "";
            public string Description { get; set; } = "";
            public bool RequiresLogin { get; set; }
        }
        
        /// <summary>
        /// 初始化平台列表
        /// </summary>
        private void InitializePlatforms()
        {
            var allPlatforms = _platformManager.GetAllPlatforms();

            foreach (var platform in allPlatforms)
            {
                _platforms.Add(new PlatformInfo
                {
                    Name = platform.PlatformName,
                    Icon = platform.PlatformIcon,
                    Description = platform.RequiresLogin ? "需要登录" : "可选登录",
                    RequiresLogin = platform.RequiresLogin
                });
            }

            // 填充平台选择下拉框
            cmbPlatform.ItemsSource = _platforms;
            cmbPlatform.SelectedIndex = 0; // 默认选择第一个平台

            // 填充平台筛选下拉框
            var filterItems = new List<string> { "全部平台" };
            filterItems.AddRange(_platforms.Select(p => $"{p.Icon} {p.Name}"));
            cmbPlatformFilter.ItemsSource = filterItems;
        }

        /// <summary>
        /// 加载账号列表
        /// </summary>
        private void LoadAccounts()
        {
            var allAccounts = _accountManager.GetAllAccounts();

            // 根据筛选条件过滤账号
            if (cmbPlatformFilter?.SelectedIndex > 0)
            {
                var selectedPlatform = _platforms[cmbPlatformFilter.SelectedIndex - 1].Name;
                allAccounts = allAccounts.Where(a => a.Platform == selectedPlatform).ToList();
            }

            lbAccounts.ItemsSource = allAccounts;

            // 选中当前活跃账号
            var currentAccount = _accountManager.GetCurrentAccount();
            if (currentAccount != null)
            {
                lbAccounts.SelectedItem = currentAccount;
            }

            UpdateAccountStats();
        }

        /// <summary>
        /// 更新账号统计信息
        /// </summary>
        private void UpdateAccountStats()
        {
            var stats = _accountManager.GetPlatformStats();
            var totalAccounts = _accountManager.GetAllAccounts().Count;
            var validAccounts = _accountManager.GetAllAccounts().Count(a => a.IsValid);

            txtAccountStats.Text = $"📊 总计: {totalAccounts}个账号 | 有效: {validAccounts}个 | 平台: {stats.Count}个";
        }
        
        private void LbAccounts_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedAccount = lbAccounts.SelectedItem as AccountInfo;
            btnDeleteAccount.IsEnabled = _selectedAccount != null;
            btnUseAccount.IsEnabled = _selectedAccount != null;
            btnValidateAccount.IsEnabled = _selectedAccount != null;

            if (_selectedAccount != null)
            {
                // 在平台选择框中选择对应平台
                var platformInfo = _platforms.FirstOrDefault(p => p.Name == _selectedAccount.Platform);
                if (platformInfo != null)
                {
                    cmbPlatform.SelectedItem = platformInfo;
                }

                txtUsername.Text = _selectedAccount.Username;
                txtNickname.Text = _selectedAccount.Nickname ?? "";
                txtNotes.Text = _selectedAccount.Notes ?? "";
                // 出于安全考虑，不显示密码
                txtPassword.Password = "";
                txtCookie.Text = _selectedAccount.Cookie ?? "";
            }
        }

        private void CmbPlatformFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            LoadAccounts();
        }

        private void CmbPlatform_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 当平台改变时，可以在这里添加特定平台的提示信息
            var selectedPlatform = cmbPlatform.SelectedItem as PlatformInfo;
            if (selectedPlatform != null && selectedPlatform.RequiresLogin)
            {
                // 可以显示该平台需要登录的提示
            }
        }
        
        private void BtnAddAccount_Click(object sender, RoutedEventArgs e)
        {
            var selectedPlatform = cmbPlatform.SelectedItem as PlatformInfo;
            if (selectedPlatform == null)
            {
                MessageBox.Show("请选择平台", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Password))
            {
                MessageBox.Show("请输入密码", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var account = new AccountInfo(selectedPlatform.Name, selectedPlatform.Icon, txtUsername.Text.Trim())
            {
                Password = txtPassword.Password,
                Nickname = txtNickname.Text.Trim(),
                Notes = txtNotes.Text.Trim()
            };

            _accountManager.AddAccount(account);

            LoadAccounts();
            ClearInputs();

            MessageBox.Show($"✅ {selectedPlatform.Icon} {selectedPlatform.Name} 账号添加成功！", "添加成功",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void BtnAddCookie_Click(object sender, RoutedEventArgs e)
        {
            var selectedPlatform = cmbPlatform.SelectedItem as PlatformInfo;
            if (selectedPlatform == null)
            {
                MessageBox.Show("请选择平台", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtCookie.Text))
            {
                MessageBox.Show("请输入Cookie", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var account = AccountInfo.CreateWithCookie(selectedPlatform.Name, selectedPlatform.Icon,
                                                     txtUsername.Text.Trim(), txtCookie.Text.Trim());
            account.Nickname = txtNickname.Text.Trim();
            account.Notes = txtNotes.Text.Trim();

            _accountManager.AddAccount(account);

            LoadAccounts();
            ClearInputs();

            MessageBox.Show($"🍪 {selectedPlatform.Icon} {selectedPlatform.Name} Cookie账号添加成功！", "添加成功",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private async void BtnTestLogin_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("请输入用户名", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            btnTestLogin.IsEnabled = false;
            btnTestLogin.Content = "测试中...";

            try
            {
                LoginResult result;

                if (!string.IsNullOrWhiteSpace(txtCookie.Text))
                {
                    // 测试Cookie登录
                    result = await _loginService.ValidateCookieAsync(txtCookie.Text);
                }
                else if (!string.IsNullOrWhiteSpace(txtPassword.Password))
                {
                    // 测试用户名密码登录
                    result = await _loginService.LoginWithPasswordAsync(txtUsername.Text, txtPassword.Password);
                }
                else
                {
                    MessageBox.Show("请输入密码或Cookie", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (result.Success)
                {
                    var message = $"登录测试成功！\n{result.Message}";
                    if (result.UserInfo != null)
                    {
                        message += $"\n用户ID: {result.UserInfo.UserId}";
                        message += $"\n用户名: {result.UserInfo.Username}";
                        message += $"\n会员状态: {(result.UserInfo.IsVip ? "大会员" : "普通用户")}";
                    }
                    MessageBox.Show(message, "登录成功", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 如果Cookie为空，自动填入获取到的Cookie
                    if (string.IsNullOrWhiteSpace(txtCookie.Text) && !string.IsNullOrWhiteSpace(result.Cookie))
                    {
                        txtCookie.Text = result.Cookie;
                    }
                }
                else
                {
                    MessageBox.Show($"登录测试失败：{result.Message}", "登录失败", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"登录测试出错：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnTestLogin.IsEnabled = true;
                btnTestLogin.Content = "测试登录";
            }
        }
        
        private void BtnDeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;
            
            var result = MessageBox.Show($"确定要删除账号 '{_selectedAccount.Username}' 吗？", 
                                       "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _accountManager.RemoveAccount(_selectedAccount);
                LoadAccounts();
                ClearInputs();
            }
        }
        
        private void BtnUseAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;
            
            _accountManager.SetCurrentAccount(_selectedAccount.Platform, _selectedAccount);
            SelectedAccount = _selectedAccount;
            
            MessageBox.Show($"已切换到账号: {_selectedAccount.Username}", "提示", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        private void BtnOpenAccountFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePath = _accountManager.GetAccountFilePath();
                Process.Start(new ProcessStartInfo
                {
                    FileName = "notepad.exe",
                    Arguments = filePath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开账号文件: {ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private async void BtnValidateAccount_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedAccount == null) return;

            btnValidateAccount.IsEnabled = false;
            btnValidateAccount.Content = "🔄 验证中...";

            try
            {
                var isValid = await _accountManager.ValidateAccountAsync(_selectedAccount);
                var status = isValid ? "✅ 有效" : "❌ 无效";
                var message = isValid ?
                    $"账号验证成功！\n\n{_selectedAccount.PlatformDisplayName}\n用户: {_selectedAccount.DisplayName}" :
                    $"账号验证失败！\n\n{_selectedAccount.PlatformDisplayName}\n用户: {_selectedAccount.DisplayName}\n\n请检查账号信息是否正确。";

                MessageBox.Show(message, $"账号验证结果 - {status}",
                              MessageBoxButton.OK,
                              isValid ? MessageBoxImage.Information : MessageBoxImage.Warning);

                LoadAccounts(); // 刷新列表以更新验证状态
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证过程中发生错误：\n{ex.Message}", "验证错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                btnValidateAccount.IsEnabled = true;
                btnValidateAccount.Content = "🔍 验证";
            }
        }

        private void BtnRefreshAccounts_Click(object sender, RoutedEventArgs e)
        {
            LoadAccounts();
            MessageBox.Show("账号列表已刷新", "刷新完成", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            _loginService?.Dispose();
            base.OnClosed(e);
        }
        
        private void ClearInputs()
        {
            txtUsername.Text = "";
            txtNickname.Text = "";
            txtNotes.Text = "";
            txtPassword.Password = "";
            txtCookie.Text = "";
            cmbPlatform.SelectedIndex = 0; // 重置为第一个平台
        }
    }
}
