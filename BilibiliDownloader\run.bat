@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo ========================================
echo    Bilibili下载器 - 启动脚本
echo ========================================
echo.
echo 当前目录: %CD%
echo.

REM 检查.NET SDK
echo 检查 .NET SDK...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET SDK 未安装或不在PATH中
    echo 请从 https://dotnet.microsoft.com/download 下载安装
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('dotnet --version') do echo ✅ .NET SDK 版本: %%i
)
echo.

REM 检查项目文件
echo 检查项目文件...
if exist "BilibiliDownloader.csproj" (
    echo ✅ 找到项目文件: BilibiliDownloader.csproj
) else (
    echo ❌ 未找到项目文件
    echo 请确保在正确的目录中运行此脚本
    dir *.csproj 2>nul
    echo.
    pause
    exit /b 1
)
echo.

REM 终止可能运行的实例
echo 检查并终止已运行的实例...
taskkill /f /im BilibiliDownloader.exe >nul 2>&1
if not errorlevel 1 (
    echo ✅ 已终止运行中的实例
    timeout /t 2 >nul
) else (
    echo ℹ️  没有运行中的实例
)
echo.

REM 清理项目
echo 1. 清理项目...
dotnet clean --verbosity quiet
if errorlevel 1 (
    echo ❌ 清理失败!
    echo 尝试手动删除 bin 和 obj 文件夹...
    if exist "bin" rmdir /s /q "bin" >nul 2>&1
    if exist "obj" rmdir /s /q "obj" >nul 2>&1
    echo ✅ 手动清理完成
) else (
    echo ✅ 清理成功
)
echo.

REM 恢复依赖
echo 2. 恢复依赖...
dotnet restore --verbosity quiet
if errorlevel 1 (
    echo ❌ 依赖恢复失败!
    echo 详细错误信息：
    dotnet restore
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 依赖恢复成功
)
echo.

REM 编译项目
echo 3. 编译项目...
dotnet build --verbosity quiet
if errorlevel 1 (
    echo ❌ 编译失败!
    echo 详细错误信息：
    dotnet build
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 编译成功
)
echo.

REM 运行程序
echo 4. 启动程序...
echo ========================================
echo.
start "" dotnet run
if errorlevel 1 (
    echo ❌ 程序启动失败!
    echo 尝试直接运行...
    dotnet run
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 程序已在后台启动
    echo.
    echo 如果程序没有显示，请检查任务栏
    echo 或者按任意键直接运行程序...
    pause >nul
    dotnet run
)

echo.
echo 程序已退出
pause
