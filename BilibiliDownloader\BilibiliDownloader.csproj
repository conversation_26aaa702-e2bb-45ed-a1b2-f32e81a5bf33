﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon />
    <StartupObject />
    <AssemblyTitle>Bilibili下载器</AssemblyTitle>
    <AssemblyDescription>安全高效的Bilibili视频下载工具</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <!-- 确保BBDown.exe和ffmpeg.exe被复制到输出目录 -->
  <ItemGroup>
    <None Include="BBDown.exe" CopyToOutputDirectory="PreserveNewest" Condition="Exists('BBDown.exe')" />
    <None Include="ffmpeg.exe" CopyToOutputDirectory="PreserveNewest" Condition="Exists('ffmpeg.exe')" />
  </ItemGroup>

</Project>
