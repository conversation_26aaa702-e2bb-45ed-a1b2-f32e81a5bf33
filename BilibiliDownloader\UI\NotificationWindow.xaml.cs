using System;
using System.Windows;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace BilibiliDownloader.UI
{
    public partial class NotificationWindow : Window
    {
        private DispatcherTimer _autoCloseTimer;
        private bool _isClosing = false;

        public NotificationWindow()
        {
            InitializeComponent();
            SetupWindow();
            SetupAutoClose();
        }

        public NotificationWindow(string title, string message) : this()
        {
            txtTitle.Text = title;
            txtMessage.Text = message;
        }

        private void SetupWindow()
        {
            // 设置窗口位置到右下角
            var workingArea = SystemParameters.WorkArea;
            Left = workingArea.Right - Width - 20;
            Top = workingArea.Bottom - Height - 20;

            // 设置初始透明度为0，准备动画
            Opacity = 0;
        }

        private void SetupAutoClose()
        {
            // 5秒后自动关闭
            _autoCloseTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            _autoCloseTimer.Tick += (s, e) => CloseWithAnimation();
            _autoCloseTimer.Start();
        }

        public void ShowWithAnimation()
        {
            Show();
            
            // 淡入动画
            var fadeIn = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(300),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
            };

            BeginAnimation(OpacityProperty, fadeIn);
        }

        private void CloseWithAnimation()
        {
            if (_isClosing) return;
            _isClosing = true;

            _autoCloseTimer?.Stop();

            // 淡出动画
            var fadeOut = new DoubleAnimation
            {
                From = Opacity,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(200),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
            };

            fadeOut.Completed += (s, e) => Close();
            BeginAnimation(OpacityProperty, fadeOut);
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            CloseWithAnimation();
        }

        protected override void OnMouseEnter(System.Windows.Input.MouseEventArgs e)
        {
            base.OnMouseEnter(e);
            // 鼠标悬停时停止自动关闭
            _autoCloseTimer?.Stop();
        }

        protected override void OnMouseLeave(System.Windows.Input.MouseEventArgs e)
        {
            base.OnMouseLeave(e);
            // 鼠标离开时重新开始自动关闭倒计时
            if (!_isClosing)
            {
                _autoCloseTimer?.Start();
            }
        }

        public static void ShowNotification(string title, string message)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var notification = new NotificationWindow(title, message);
                notification.ShowWithAnimation();
            });
        }
    }
}
