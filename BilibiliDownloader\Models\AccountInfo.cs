using System;

namespace BilibiliDownloader.Models
{
    public class AccountInfo
    {
        public string Username { get; set; }
        public string Password { get; set; }
        public string Cookie { get; set; }
        public DateTime LastLoginTime { get; set; }
        public bool IsActive { get; set; }
        public string UserId { get; set; }
        public string Nickname { get; set; }
        
        public AccountInfo()
        {
            LastLoginTime = DateTime.Now;
            IsActive = false;
        }
        
        public AccountInfo(string username, string password) : this()
        {
            Username = username;
            Password = password;
        }

        public static AccountInfo CreateWithCookie(string username, string cookie)
        {
            var account = new AccountInfo();
            account.Username = username;
            account.Cookie = cookie;
            return account;
        }
        
        public override string ToString()
        {
            return string.IsNullOrEmpty(Nickname) ? Username : $"{Nickname} ({Username})";
        }
    }
}
