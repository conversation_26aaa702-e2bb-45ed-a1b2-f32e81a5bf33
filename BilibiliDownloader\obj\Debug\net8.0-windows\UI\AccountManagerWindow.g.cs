﻿#pragma checksum "..\..\..\..\UI\AccountManagerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BF09E1D6BDD852DD283775E48027D7AA601F4035"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using BilibiliDownloader.UI;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace BilibiliDownloader.UI {
    
    
    /// <summary>
    /// AccountManagerWindow
    /// </summary>
    public partial class AccountManagerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 50 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbPlatformFilter;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox lbAccounts;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDeleteAccount;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnUseAccount;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnValidateAccount;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbPlatform;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtUsername;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtNickname;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtNotes;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl tcLoginMethod;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox txtPassword;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkSavePassword;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddAccount;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTestLogin;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCookie;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddCookie;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtAccountStats;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefreshAccounts;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOpenAccountFile;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\UI\AccountManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/BilibiliDownloader;V2.0.0.0;component/ui/accountmanagerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\UI\AccountManagerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.cmbPlatformFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 50 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.cmbPlatformFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbPlatformFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.lbAccounts = ((System.Windows.Controls.ListBox)(target));
            
            #line 55 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.lbAccounts.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LbAccounts_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnDeleteAccount = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnDeleteAccount.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteAccount_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnUseAccount = ((System.Windows.Controls.Button)(target));
            
            #line 85 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnUseAccount.Click += new System.Windows.RoutedEventHandler(this.BtnUseAccount_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnValidateAccount = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnValidateAccount.Click += new System.Windows.RoutedEventHandler(this.BtnValidateAccount_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.cmbPlatform = ((System.Windows.Controls.ComboBox)(target));
            
            #line 97 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.cmbPlatform.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbPlatform_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.txtUsername = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txtNickname = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.txtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.tcLoginMethod = ((System.Windows.Controls.TabControl)(target));
            return;
            case 11:
            this.txtPassword = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 12:
            this.chkSavePassword = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.btnAddAccount = ((System.Windows.Controls.Button)(target));
            
            #line 140 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnAddAccount.Click += new System.Windows.RoutedEventHandler(this.BtnAddAccount_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btnTestLogin = ((System.Windows.Controls.Button)(target));
            
            #line 141 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnTestLogin.Click += new System.Windows.RoutedEventHandler(this.BtnTestLogin_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.txtCookie = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.btnAddCookie = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnAddCookie.Click += new System.Windows.RoutedEventHandler(this.BtnAddCookie_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.txtAccountStats = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.btnRefreshAccounts = ((System.Windows.Controls.Button)(target));
            
            #line 177 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnRefreshAccounts.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshAccounts_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.btnOpenAccountFile = ((System.Windows.Controls.Button)(target));
            
            #line 178 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnOpenAccountFile.Click += new System.Windows.RoutedEventHandler(this.BtnOpenAccountFile_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\UI\AccountManagerWindow.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

