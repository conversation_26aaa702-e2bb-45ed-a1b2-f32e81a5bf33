using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class YouTubeDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "YouTube";
        public string PlatformIcon => "🎬";
        public bool RequiresLogin => false;

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?youtube\.com/watch\?v=[\w-]+",
            @"https?://youtu\.be/[\w-]+",
            @"https?://(?:www\.)?youtube\.com/shorts/[\w-]+",
            @"https?://(?:www\.)?youtube\.com/embed/[\w-]+"
        };

        public YouTubeDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析YouTube视频: {url}");

                // 提取视频ID
                var videoId = ExtractVideoId(url);
                if (string.IsNullOrEmpty(videoId))
                {
                    throw new Exception("无法提取视频ID");
                }

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                // 获取视频信息
                var videoInfo = await GetYouTubeVideoInfoAsync(videoId);
                if (videoInfo != null)
                {
                    videoInfo.Platform = PlatformName;
                    videoInfo.OriginalUrl = url;
                }

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"YouTube解析失败: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> GetYouTubeVideoInfoAsync(string videoId)
        {
            try
            {
                // 使用YouTube的oEmbed API获取基本信息
                var oembedUrl = $"https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v={videoId}&format=json";
                var oembedResponse = await _httpClient.GetStringAsync(oembedUrl);
                var oembedData = JsonSerializer.Deserialize<JsonElement>(oembedResponse);

                // 获取网页内容以提取更多信息
                var pageUrl = $"https://www.youtube.com/watch?v={videoId}";
                var pageContent = await _httpClient.GetStringAsync(pageUrl);

                // 解析基本信息
                var title = oembedData.TryGetProperty("title", out var titleProp) ? titleProp.GetString() : $"YouTube视频 {videoId}";
                var author = oembedData.TryGetProperty("author_name", out var authorProp) ? authorProp.GetString() : "未知作者";
                var thumbnailUrl = oembedData.TryGetProperty("thumbnail_url", out var thumbProp) ? thumbProp.GetString() : "";

                // 尝试从页面内容中提取更多信息
                var duration = ExtractDurationFromPage(pageContent);
                var description = ExtractDescriptionFromPage(pageContent);
                var viewCount = ExtractViewCountFromPage(pageContent);

                return new VideoInfo
                {
                    VideoId = videoId,
                    Title = title ?? $"YouTube视频 {videoId}",
                    Author = author ?? "未知作者",
                    Description = description,
                    CoverUrl = thumbnailUrl ?? "",
                    Duration = duration,
                    ViewCount = viewCount,
                    PublishTime = DateTime.Now, // YouTube API限制，暂时使用当前时间
                    IsCollection = false,
                    IsPaymentRequired = false,
                    Pages = new List<VideoPage>
                    {
                        new VideoPage
                        {
                            Page = 1,
                            Cid = 0,
                            Part = title ?? $"YouTube视频 {videoId}",
                            Duration = duration
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取YouTube视频信息失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "2160p", Description = "4K 超高清", Width = 3840, Height = 2160, Format = "mp4" },
                new() { Quality = "1440p", Description = "2K 高清", Width = 2560, Height = 1440, Format = "mp4" },
                new() { Quality = "1080p", Description = "全高清", Width = 1920, Height = 1080, Format = "mp4" },
                new() { Quality = "720p", Description = "高清", Width = 1280, Height = 720, Format = "mp4" },
                new() { Quality = "480p", Description = "标清", Width = 854, Height = 480, Format = "mp4" },
                new() { Quality = "360p", Description = "流畅", Width = 640, Height = 360, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"获取YouTube下载链接: {videoInfo.VideoId}, 质量: {quality}");

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                // 尝试获取实际的下载链接
                var downloadUrls = await GetYouTubeDownloadUrlsAsync(videoInfo.VideoId, quality);

                if (downloadUrls.Count == 0)
                {
                    // 如果无法获取实际链接，提供说明
                    Logger.Instance.Warning("无法获取YouTube直接下载链接，建议使用yt-dlp等专业工具");
                    throw new Exception("YouTube下载需要使用专业工具，如yt-dlp。由于YouTube的反爬虫机制，无法直接获取下载链接。");
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取YouTube下载链接失败: {ex.Message}");
                throw new Exception($"YouTube下载失败: {ex.Message}\n\n建议：\n1. 使用yt-dlp等专业工具\n2. 检查网络连接\n3. 确认视频是否可用");
            }
        }

        private async Task<List<DownloadUrl>> GetYouTubeDownloadUrlsAsync(string videoId, string quality)
        {
            try
            {
                // 注意：由于YouTube的反爬虫机制，这里只是示例实现
                // 实际使用中建议集成yt-dlp或类似工具

                Logger.Instance.Warning("YouTube下载功能需要集成专业工具（如yt-dlp）才能正常工作");
                return new List<DownloadUrl>();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"YouTube下载URL获取异常: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            // YouTube通常不需要特殊的账号验证
            return true;
        }

        private long ExtractDurationFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取时长信息
                var durationMatch = Regex.Match(pageContent, @"""lengthSeconds"":""(\d+)""");
                if (durationMatch.Success && long.TryParse(durationMatch.Groups[1].Value, out var duration))
                {
                    return duration;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private string ExtractDescriptionFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取描述信息
                var descMatch = Regex.Match(pageContent, @"""shortDescription"":""([^""]+)""");
                if (descMatch.Success)
                {
                    return descMatch.Groups[1].Value.Replace("\\n", "\n").Replace("\\\"", "\"");
                }
                return "";
            }
            catch
            {
                return "";
            }
        }

        private long ExtractViewCountFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取观看次数
                var viewMatch = Regex.Match(pageContent, @"""viewCount"":""(\d+)""");
                if (viewMatch.Success && long.TryParse(viewMatch.Groups[1].Value, out var viewCount))
                {
                    return viewCount;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private string ExtractVideoId(string url)
        {
            var patterns = new[]
            {
                @"(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/|youtube\.com/shorts/)([\w-]+)",
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        private async Task<dynamic> GetVideoInfoFromApi(string videoId)
        {
            // 这里应该调用实际的YouTube API
            // 目前返回模拟数据
            await Task.Delay(100); // 模拟网络延迟

            return new
            {
                title = "YouTube视频标题",
                uploader = "频道名称",
                duration = 300,
                description = "视频描述",
                thumbnail = "https://img.youtube.com/vi/" + videoId + "/maxresdefault.jpg",
                upload_date = DateTime.Now.ToString("yyyy-MM-dd"),
                view_count = 1000000
            };
        }

        private long EstimateFileSize(string quality)
        {
            return quality switch
            {
                "2160p" => 2000 * 1024 * 1024L, // 2GB
                "1440p" => 1000 * 1024 * 1024L, // 1GB
                "1080p" => 500 * 1024 * 1024L,  // 500MB
                "720p" => 200 * 1024 * 1024L,   // 200MB
                "480p" => 100 * 1024 * 1024L,   // 100MB
                "360p" => 50 * 1024 * 1024L,    // 50MB
                _ => 100 * 1024 * 1024L
            };
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
