using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class YouTubeDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "YouTube";
        public string PlatformIcon => "🎬";
        public bool RequiresLogin => false;

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?youtube\.com/watch\?v=[\w-]+",
            @"https?://youtu\.be/[\w-]+",
            @"https?://(?:www\.)?youtube\.com/shorts/[\w-]+",
            @"https?://(?:www\.)?youtube\.com/embed/[\w-]+"
        };

        public YouTubeDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析YouTube视频: {url}");

                // 提取视频ID
                var videoId = ExtractVideoId(url);
                if (string.IsNullOrEmpty(videoId))
                {
                    throw new Exception("无法提取视频ID");
                }

                // 使用yt-dlp或类似工具的API（这里是模拟实现）
                var videoInfo = await GetVideoInfoFromApi(videoId);
                
                return new VideoInfo
                {
                    Title = videoInfo.title ?? "未知标题",
                    Author = videoInfo.uploader ?? "未知作者",
                    Duration = videoInfo.duration ?? 0,
                    Description = videoInfo.description ?? "",
                    CoverUrl = videoInfo.thumbnail ?? "",
                    VideoId = videoId,
                    Url = url,
                    Platform = PlatformName,
                    PublishTime = DateTime.TryParse(videoInfo.upload_date, out DateTime date) ? date : DateTime.Now,
                    ViewCount = videoInfo.view_count ?? 0,
                    IsCollection = false,
                    IsPaymentRequired = false
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"YouTube解析失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "2160p", Description = "4K 超高清", Width = 3840, Height = 2160, Format = "mp4" },
                new() { Quality = "1440p", Description = "2K 高清", Width = 2560, Height = 1440, Format = "mp4" },
                new() { Quality = "1080p", Description = "全高清", Width = 1920, Height = 1080, Format = "mp4" },
                new() { Quality = "720p", Description = "高清", Width = 1280, Height = 720, Format = "mp4" },
                new() { Quality = "480p", Description = "标清", Width = 854, Height = 480, Format = "mp4" },
                new() { Quality = "360p", Description = "流畅", Width = 640, Height = 360, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                // 这里应该调用实际的YouTube API或yt-dlp
                // 目前返回模拟数据
                var downloadUrls = new List<DownloadUrl>
                {
                    new()
                    {
                        Url = $"https://example.com/youtube/{videoInfo.VideoId}/{quality}.mp4",
                        Quality = quality,
                        Format = "mp4",
                        FileSize = EstimateFileSize(quality),
                        Headers = new Dictionary<string, string>
                        {
                            ["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                        }
                    }
                };

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取YouTube下载链接失败: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            // YouTube通常不需要特殊的账号验证
            return true;
        }

        private string ExtractVideoId(string url)
        {
            var patterns = new[]
            {
                @"(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/|youtube\.com/shorts/)([\w-]+)",
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        private async Task<dynamic> GetVideoInfoFromApi(string videoId)
        {
            // 这里应该调用实际的YouTube API
            // 目前返回模拟数据
            await Task.Delay(100); // 模拟网络延迟

            return new
            {
                title = "YouTube视频标题",
                uploader = "频道名称",
                duration = 300,
                description = "视频描述",
                thumbnail = "https://img.youtube.com/vi/" + videoId + "/maxresdefault.jpg",
                upload_date = DateTime.Now.ToString("yyyy-MM-dd"),
                view_count = 1000000
            };
        }

        private long EstimateFileSize(string quality)
        {
            return quality switch
            {
                "2160p" => 2000 * 1024 * 1024L, // 2GB
                "1440p" => 1000 * 1024 * 1024L, // 1GB
                "1080p" => 500 * 1024 * 1024L,  // 500MB
                "720p" => 200 * 1024 * 1024L,   // 200MB
                "480p" => 100 * 1024 * 1024L,   // 100MB
                "360p" => 50 * 1024 * 1024L,    // 50MB
                _ => 100 * 1024 * 1024L
            };
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
