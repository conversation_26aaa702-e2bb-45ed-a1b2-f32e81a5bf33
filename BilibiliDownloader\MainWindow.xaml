﻿<Window x:Class="BilibiliDownloader.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:BilibiliDownloader"
        mc:Ignorable="d"
        Title="Bilibili下载器 - 安全测试工具" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <Border Grid.Row="0" Background="#FF2B2B2B" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="Bilibili下载器" Foreground="White" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                <TextBlock Text="  |  安全测试专用工具" Foreground="#FFAAAAAA" FontSize="12" VerticalAlignment="Center"/>
                <Button Name="btnAccountManager" Content="账号管理" Margin="20,0,0,0" Padding="10,5" Click="BtnAccountManager_Click"/>
                <TextBlock Name="txtCurrentUser" Text="未登录" Foreground="#FFAAAAAA" Margin="20,0,0,0" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- 输入区域 -->
        <Border Grid.Row="1" Background="#FFF5F5F5" Padding="15">
            <StackPanel>
                <TextBlock Text="视频链接输入:" FontWeight="Bold" Margin="0,0,0,5"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <TextBox Name="txtVideoUrl" Grid.Column="0" Height="30" VerticalContentAlignment="Center"
                             Text="https://www.bilibili.com/video/" FontSize="12"/>
                    <Button Name="btnAnalyze" Grid.Column="1" Content="解析视频" Margin="10,0,0,0" Padding="15,5" Click="BtnAnalyze_Click"/>
                    <Button Name="btnDownload" Grid.Column="2" Content="开始下载" Margin="10,0,0,0" Padding="15,5" Click="BtnDownload_Click" IsEnabled="False"/>
                </Grid>

                <!-- 下载选项 -->
                <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                    <TextBlock Text="清晰度:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox Name="cmbQuality" Width="120" SelectedIndex="0">
                        <ComboBoxItem Content="1080P"/>
                        <ComboBoxItem Content="720P"/>
                        <ComboBoxItem Content="480P"/>
                        <ComboBoxItem Content="360P"/>
                    </ComboBox>
                    <CheckBox Name="chkDownloadSeries" Content="下载整个合集" Margin="20,0,0,0" VerticalAlignment="Center"/>
                    <CheckBox Name="chkBypassPayment" Content="尝试绕过付费检测" Margin="20,0,0,0" VerticalAlignment="Center" Foreground="Red"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 下载列表 -->
            <GroupBox Grid.Column="0" Header="下载列表" Margin="0,0,5,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <ListView Name="lvDownloads" Grid.Row="0">
                        <ListView.View>
                            <GridView>
                                <GridViewColumn Header="标题" Width="200" DisplayMemberBinding="{Binding Title}"/>
                                <GridViewColumn Header="状态" Width="80" DisplayMemberBinding="{Binding Status}"/>
                                <GridViewColumn Header="进度" Width="100">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <ProgressBar Value="{Binding Progress}" Maximum="100" Height="15"/>
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="大小" Width="80" DisplayMemberBinding="{Binding Size}"/>
                                <GridViewColumn Header="速度" Width="80" DisplayMemberBinding="{Binding Speed}"/>
                            </GridView>
                        </ListView.View>
                    </ListView>
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,10,0,0">
                        <Button Name="btnPauseAll" Content="暂停全部" Padding="10,5" Click="BtnPauseAll_Click"/>
                        <Button Name="btnResumeAll" Content="继续全部" Margin="10,0,0,0" Padding="10,5" Click="BtnResumeAll_Click"/>
                        <Button Name="btnClearCompleted" Content="清除已完成" Margin="10,0,0,0" Padding="10,5" Click="BtnClearCompleted_Click"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- 视频信息和日志 -->
            <Grid Grid.Column="1" Margin="5,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 视频信息 -->
                <GroupBox Grid.Row="0" Header="视频信息" Margin="0,0,0,5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <ScrollViewer Grid.Row="0">
                            <StackPanel Name="pnlVideoInfo">
                                <TextBlock Text="请输入视频链接并点击解析" Foreground="Gray" TextAlignment="Center" Margin="10"/>
                            </StackPanel>
                        </ScrollViewer>

                        <!-- 合集视频列表 -->
                        <Expander Grid.Row="1" Name="expSeriesList" Header="合集视频列表" IsExpanded="False" Visibility="Collapsed" Margin="0,5,0,0">
                            <ListBox Name="lbSeriesVideos" MaxHeight="150" SelectionChanged="LbSeriesVideos_SelectionChanged">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" Margin="2">
                                            <TextBlock Text="{Binding Page}" FontWeight="Bold" Width="30" Foreground="Blue"/>
                                            <TextBlock Text="{Binding Part}" TextWrapping="Wrap" Margin="5,0,0,0"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Expander>
                    </Grid>
                </GroupBox>

                <!-- 日志输出 -->
                <GroupBox Grid.Row="1" Header="日志输出" Margin="0,5,0,0">
                    <ScrollViewer Name="svLog" VerticalScrollBarVisibility="Auto">
                        <TextBox Name="txtLog" IsReadOnly="True" Background="Black" Foreground="Lime"
                                 FontFamily="Consolas" FontSize="10" TextWrapping="Wrap" BorderThickness="0"/>
                    </ScrollViewer>
                </GroupBox>
            </Grid>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="3" Background="#FF2B2B2B" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBlock Name="txtStatus" Grid.Column="0" Text="就绪" Foreground="White" VerticalAlignment="Center"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="活跃下载:" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Name="txtActiveDownloads" Text="0" Foreground="Lime" Margin="5,0,15,0" VerticalAlignment="Center"/>
                    <TextBlock Text="总下载速度:" Foreground="White" VerticalAlignment="Center"/>
                    <TextBlock Name="txtTotalSpeed" Text="0 KB/s" Foreground="Lime" Margin="5,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
