using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class TikTokDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "TikTok";
        public string PlatformIcon => "🎵";
        public bool RequiresLogin => false;

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?tiktok\.com/@[\w.-]+/video/\d+",
            @"https?://vm\.tiktok\.com/[\w]+",
            @"https?://(?:www\.)?tiktok\.com/t/[\w]+",
            @"https?://m\.tiktok\.com/v/\d+"
        };

        public TikTokDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析TikTok视频: {url}");

                // 处理短链接
                var fullUrl = await ResolveShortUrl(url);
                var videoId = ExtractVideoId(fullUrl);

                if (string.IsNullOrEmpty(videoId))
                {
                    throw new Exception("无法提取视频ID");
                }

                var videoInfo = await GetVideoInfoFromApi(videoId, cookie);
                
                return new VideoInfo
                {
                    Title = videoInfo.title ?? "TikTok视频",
                    Author = videoInfo.author ?? "未知作者",
                    Duration = videoInfo.duration ?? 15, // TikTok视频通常很短
                    Description = videoInfo.description ?? "",
                    CoverUrl = videoInfo.cover ?? "",
                    VideoId = videoId,
                    Url = fullUrl,
                    Platform = PlatformName,
                    PublishTime = DateTime.TryParse(videoInfo.create_time, out DateTime date) ? date : DateTime.Now,
                    ViewCount = videoInfo.play_count ?? 0,
                    IsCollection = false,
                    IsPaymentRequired = false
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"TikTok解析失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "HD", Description = "高清 (1080p)", Width = 1080, Height = 1920, Format = "mp4" },
                new() { Quality = "SD", Description = "标清 (720p)", Width = 720, Height = 1280, Format = "mp4" },
                new() { Quality = "LD", Description = "流畅 (480p)", Width = 480, Height = 854, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                var downloadUrls = new List<DownloadUrl>();

                // 视频下载链接
                downloadUrls.Add(new DownloadUrl
                {
                    Url = $"https://example.com/tiktok/{videoInfo.VideoId}/{quality}.mp4",
                    Quality = quality,
                    Format = "mp4",
                    FileSize = EstimateFileSize(quality),
                    Headers = new Dictionary<string, string>
                    {
                        ["User-Agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)",
                        ["Referer"] = "https://www.tiktok.com/"
                    }
                });

                // 音频下载链接（可选）
                downloadUrls.Add(new DownloadUrl
                {
                    Url = $"https://example.com/tiktok/{videoInfo.VideoId}/audio.mp3",
                    Quality = "Audio",
                    Format = "mp3",
                    FileSize = 2 * 1024 * 1024, // 2MB
                    Headers = new Dictionary<string, string>
                    {
                        ["User-Agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)",
                        ["Referer"] = "https://www.tiktok.com/"
                    }
                });

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取TikTok下载链接失败: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                if (string.IsNullOrEmpty(cookie)) return true;

                var request = new HttpRequestMessage(HttpMethod.Get, "https://www.tiktok.com/api/user/info/");
                request.Headers.Add("Cookie", cookie);

                var response = await _httpClient.SendAsync(request);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> ResolveShortUrl(string url)
        {
            try
            {
                if (url.Contains("vm.tiktok.com") || url.Contains("/t/"))
                {
                    var request = new HttpRequestMessage(HttpMethod.Head, url);
                    var response = await _httpClient.SendAsync(request);
                    
                    if (response.Headers.Location != null)
                    {
                        return response.Headers.Location.ToString();
                    }
                }
                return url;
            }
            catch
            {
                return url;
            }
        }

        private string ExtractVideoId(string url)
        {
            var patterns = new[]
            {
                @"tiktok\.com/@[\w.-]+/video/(\d+)",
                @"tiktok\.com/v/(\d+)",
                @"video/(\d+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        private async Task<dynamic> GetVideoInfoFromApi(string videoId, string? cookie)
        {
            // 模拟API调用
            await Task.Delay(100);

            return new
            {
                title = "TikTok精彩视频",
                author = "@tiktok_user",
                duration = 15,
                description = "有趣的TikTok视频内容",
                cover = $"https://example.com/tiktok/{videoId}/cover.jpg",
                create_time = DateTime.Now.ToString("yyyy-MM-dd"),
                play_count = 50000
            };
        }

        private long EstimateFileSize(string quality)
        {
            return quality switch
            {
                "HD" => 20 * 1024 * 1024L,  // 20MB
                "SD" => 10 * 1024 * 1024L,  // 10MB
                "LD" => 5 * 1024 * 1024L,   // 5MB
                _ => 10 * 1024 * 1024L
            };
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
