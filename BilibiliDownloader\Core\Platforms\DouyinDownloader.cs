using System.Text.RegularExpressions;
using System.Text.Json;
using System.Net.Http;
using BilibiliDownloader.Models;
using BilibiliDownloader.Utils;

namespace BilibiliDownloader.Core.Platforms
{
    public class DouyinDownloader : IPlatformDownloader
    {
        private readonly HttpClient _httpClient;

        public string PlatformName => "抖音";
        public string PlatformIcon => "🎵";
        public bool RequiresLogin => false;

        public string[] SupportedUrlPatterns => new[]
        {
            @"https?://(?:www\.)?douyin\.com/video/\d+",
            @"https?://v\.douyin\.com/[\w]+",
            @"https?://(?:www\.)?iesdouyin\.com/share/video/\d+",
            @"https?://(?:www\.)?douyin\.com/user/[\w]+/video/\d+"
        };

        public DouyinDownloader()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", 
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1");
        }

        public bool IsUrlSupported(string url)
        {
            return SupportedUrlPatterns.Any(pattern => Regex.IsMatch(url, pattern));
        }

        public async Task<VideoInfo?> ParseVideoInfoAsync(string url, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"解析抖音视频: {url}");

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                // 处理短链接
                var fullUrl = await ResolveShortUrl(url);
                var videoId = ExtractVideoId(fullUrl);

                if (string.IsNullOrEmpty(videoId))
                {
                    throw new Exception("无法提取视频ID");
                }

                // 获取视频信息
                var videoInfo = await GetDouyinVideoInfoAsync(videoId, fullUrl);
                if (videoInfo != null)
                {
                    videoInfo.Platform = PlatformName;
                    videoInfo.OriginalUrl = url;
                }

                return videoInfo;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"抖音解析失败: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo?> GetDouyinVideoInfoAsync(string videoId, string fullUrl)
        {
            try
            {
                // 获取网页内容
                var pageContent = await _httpClient.GetStringAsync(fullUrl);

                // 从页面中提取视频信息
                var title = ExtractTitleFromPage(pageContent) ?? $"抖音视频 {videoId}";
                var author = ExtractAuthorFromPage(pageContent) ?? "未知作者";
                var description = ExtractDescriptionFromPage(pageContent) ?? "";
                var coverUrl = ExtractCoverFromPage(pageContent) ?? "";
                var duration = ExtractDurationFromPage(pageContent);

                return new VideoInfo
                {
                    VideoId = videoId,
                    Title = title,
                    Author = author,
                    Description = description,
                    CoverUrl = coverUrl,
                    Duration = duration,
                    ViewCount = 0, // 抖音不公开具体播放数
                    PublishTime = DateTime.Now, // 抖音API限制
                    IsCollection = false,
                    IsPaymentRequired = false,
                    Pages = new List<VideoPage>
                    {
                        new VideoPage
                        {
                            Page = 1,
                            Cid = 0,
                            Part = title,
                            Duration = duration
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取抖音视频信息失败: {ex.Message}");
                return null;
            }
        }

        public async Task<List<QualityOption>> GetAvailableQualitiesAsync(VideoInfo videoInfo, string? cookie = null)
        {
            var qualities = new List<QualityOption>
            {
                new() { Quality = "720p", Description = "高清", Width = 720, Height = 1280, Format = "mp4" },
                new() { Quality = "540p", Description = "标清", Width = 540, Height = 960, Format = "mp4" },
                new() { Quality = "360p", Description = "流畅", Width = 360, Height = 640, Format = "mp4" }
            };

            return qualities;
        }

        public async Task<List<DownloadUrl>> GetDownloadUrlsAsync(VideoInfo videoInfo, string quality, string? cookie = null)
        {
            try
            {
                Logger.Instance.Info($"获取抖音下载链接: {videoInfo.VideoId}, 质量: {quality}");

                // 设置Cookie（如果提供）
                if (!string.IsNullOrEmpty(cookie))
                {
                    _httpClient.DefaultRequestHeaders.Remove("Cookie");
                    _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                }

                // 尝试获取实际的下载链接
                var downloadUrls = await GetDouyinDownloadUrlsAsync(videoInfo.VideoId, quality);
                
                if (downloadUrls.Count == 0)
                {
                    Logger.Instance.Warning("无法获取抖音直接下载链接");
                    throw new Exception("抖音下载需要特殊处理。由于抖音的反爬虫机制，无法直接获取下载链接。");
                }

                return downloadUrls;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"获取抖音下载链接失败: {ex.Message}");
                throw new Exception($"抖音下载失败: {ex.Message}\n\n建议：\n1. 使用专业的抖音下载工具\n2. 检查网络连接\n3. 确认视频是否可用");
            }
        }

        private async Task<List<DownloadUrl>> GetDouyinDownloadUrlsAsync(string videoId, string quality)
        {
            try
            {
                // 注意：由于抖音的反爬虫机制，这里只是示例实现
                Logger.Instance.Warning("抖音下载功能需要集成专业工具才能正常工作");
                return new List<DownloadUrl>();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"抖音下载URL获取异常: {ex.Message}");
                return new List<DownloadUrl>();
            }
        }

        public async Task<bool> ValidateAccountAsync(string cookie)
        {
            try
            {
                // 简单验证Cookie是否有效
                _httpClient.DefaultRequestHeaders.Remove("Cookie");
                _httpClient.DefaultRequestHeaders.Add("Cookie", cookie);
                
                var response = await _httpClient.GetAsync("https://www.douyin.com/");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private string? ExtractTitleFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取标题
                var titleMatch = Regex.Match(pageContent, @"""desc"":""([^""]+)""");
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Replace("\\n", " ").Replace("\\\"", "\"");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractAuthorFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取作者信息
                var authorMatch = Regex.Match(pageContent, @"""nickname"":""([^""]+)""");
                if (authorMatch.Success)
                {
                    return authorMatch.Groups[1].Value;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractDescriptionFromPage(string pageContent)
        {
            try
            {
                // 抖音的描述通常就是标题
                return ExtractTitleFromPage(pageContent);
            }
            catch
            {
                return null;
            }
        }

        private string? ExtractCoverFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取封面图
                var coverMatch = Regex.Match(pageContent, @"""cover"":""([^""]+)""");
                if (coverMatch.Success)
                {
                    return coverMatch.Groups[1].Value.Replace("\\u002F", "/");
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private long ExtractDurationFromPage(string pageContent)
        {
            try
            {
                // 尝试从页面中提取时长
                var durationMatch = Regex.Match(pageContent, @"""duration"":(\d+)");
                if (durationMatch.Success && long.TryParse(durationMatch.Groups[1].Value, out var duration))
                {
                    return duration;
                }
                return 15; // 抖音默认15秒
            }
            catch
            {
                return 15;
            }
        }

        private async Task<string> ResolveShortUrl(string url)
        {
            try
            {
                if (url.Contains("v.douyin.com"))
                {
                    var request = new HttpRequestMessage(HttpMethod.Head, url);
                    var response = await _httpClient.SendAsync(request);
                    
                    if (response.Headers.Location != null)
                    {
                        return response.Headers.Location.ToString();
                    }
                }
                return url;
            }
            catch
            {
                return url;
            }
        }

        private string ExtractVideoId(string url)
        {
            var patterns = new[]
            {
                @"douyin\.com/video/(\d+)",
                @"iesdouyin\.com/share/video/(\d+)",
                @"video/(\d+)"
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(url, pattern);
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return "";
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
