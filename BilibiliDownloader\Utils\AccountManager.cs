using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using BilibiliDownloader.Models;

namespace BilibiliDownloader.Utils
{
    public class AccountManager
    {
        private static readonly string AccountFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "accounts.json");
        private List<AccountInfo> _accounts;
        private AccountInfo _currentAccount;
        
        public event EventHandler<AccountInfo> AccountChanged;
        
        public AccountManager()
        {
            LoadAccounts();
        }
        
        public List<AccountInfo> GetAllAccounts()
        {
            return _accounts?.ToList() ?? new List<AccountInfo>();
        }
        
        public AccountInfo GetCurrentAccount()
        {
            return _currentAccount;
        }
        
        public void SetCurrentAccount(AccountInfo account)
        {
            if (_currentAccount != null)
            {
                _currentAccount.IsActive = false;
            }
            
            _currentAccount = account;
            if (account != null)
            {
                account.IsActive = true;
                account.LastLoginTime = DateTime.Now;
            }
            
            SaveAccounts();
            AccountChanged?.Invoke(this, account);
        }
        
        public void AddAccount(AccountInfo account)
        {
            if (_accounts == null)
                _accounts = new List<AccountInfo>();
                
            // 检查是否已存在相同用户名的账号
            var existingAccount = _accounts.FirstOrDefault(a => a.Username.Equals(account.Username, StringComparison.OrdinalIgnoreCase));
            if (existingAccount != null)
            {
                // 更新现有账号
                existingAccount.Password = account.Password;
                existingAccount.Cookie = account.Cookie;
                existingAccount.LastLoginTime = DateTime.Now;
            }
            else
            {
                _accounts.Add(account);
            }
            
            SaveAccounts();
        }
        
        public void RemoveAccount(AccountInfo account)
        {
            if (_accounts != null && account != null)
            {
                _accounts.Remove(account);
                
                if (_currentAccount == account)
                {
                    _currentAccount = null;
                    AccountChanged?.Invoke(this, null);
                }
                
                SaveAccounts();
            }
        }
        
        public void LoadAccounts()
        {
            try
            {
                if (File.Exists(AccountFilePath))
                {
                    var json = File.ReadAllText(AccountFilePath);
                    _accounts = JsonConvert.DeserializeObject<List<AccountInfo>>(json) ?? new List<AccountInfo>();
                    
                    // 恢复当前活跃账号
                    _currentAccount = _accounts.FirstOrDefault(a => a.IsActive);
                }
                else
                {
                    _accounts = new List<AccountInfo>();
                }
            }
            catch (Exception ex)
            {
                // 如果加载失败，创建新的账号列表
                _accounts = new List<AccountInfo>();
                LogError($"加载账号文件失败: {ex.Message}");
            }
        }
        
        public void SaveAccounts()
        {
            try
            {
                var json = JsonConvert.SerializeObject(_accounts, Formatting.Indented);
                File.WriteAllText(AccountFilePath, json);
            }
            catch (Exception ex)
            {
                LogError($"保存账号文件失败: {ex.Message}");
            }
        }
        
        public string GetAccountFilePath()
        {
            return AccountFilePath;
        }
        
        private void LogError(string message)
        {
            // 这里可以集成到主程序的日志系统
            Console.WriteLine($"[AccountManager] {message}");
        }
    }
}
